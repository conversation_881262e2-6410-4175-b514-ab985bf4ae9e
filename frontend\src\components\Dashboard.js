import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Alert, Spin, Progress } from 'antd';
import {
  DatabaseOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import axios from 'axios';

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [healthData, setHealthData] = useState(null);
  const [metricsData, setMetricsData] = useState(null);
  const [adminStats, setAdminStats] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDashboardData();
    const interval = setInterval(fetchDashboardData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const [healthResponse, metricsResponse, adminResponse] = await Promise.all([
        axios.get('/health'),
        axios.get('/metrics'),
        axios.get('/api/v1/admin/stats')
      ]);

      setHealthData(healthResponse.data);
      setMetricsData(metricsResponse.data);
      setAdminStats(adminResponse.data);
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading && !healthData) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p>Loading dashboard...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Error Loading Dashboard"
        description={error}
        type="error"
        showIcon
        action={
          <button onClick={fetchDashboardData}>Retry</button>
        }
      />
    );
  }

  const isHealthy = healthData?.status === 'healthy';
  const dbHealthy = healthData?.components?.database === 'healthy';
  const celeryHealthy = healthData?.components?.celery === 'healthy';

  // Prepare chart data
  const sourceData = metricsData?.jobs?.by_source ? 
    Object.entries(metricsData.jobs.by_source).map(([source, count]) => ({
      name: source,
      value: count
    })) : [];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

  return (
    <div>
      {/* Health Status */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Alert
            message={`System Status: ${isHealthy ? 'Healthy' : 'Unhealthy'}`}
            type={isHealthy ? 'success' : 'error'}
            showIcon
            icon={isHealthy ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
            description={
              <div>
                Database: {dbHealthy ? '✓ Healthy' : '✗ Unhealthy'} | 
                Celery: {celeryHealthy ? '✓ Healthy' : '✗ Unhealthy'}
              </div>
            }
          />
        </Col>
      </Row>

      {/* Key Metrics */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Jobs"
              value={metricsData?.jobs?.total || 0}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Active Jobs"
              value={metricsData?.jobs?.active || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Recent Jobs (24h)"
              value={metricsData?.jobs?.recent_24h || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Recent Sessions (24h)"
              value={metricsData?.scraping?.recent_sessions_24h || 0}
              prefix={<GlobalOutlined />}
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Charts */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={12}>
          <Card title="Jobs by Source" style={{ height: 400 }}>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={sourceData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {sourceData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="Top Locations" style={{ height: 400 }}>
            <div style={{ maxHeight: 300, overflowY: 'auto' }}>
              {metricsData?.top_locations?.map((location, index) => (
                <div key={index} style={{ marginBottom: 16 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                    <span>{location.location}</span>
                    <span>{location.count} jobs</span>
                  </div>
                  <Progress 
                    percent={Math.round((location.count / (metricsData?.jobs?.total || 1)) * 100)} 
                    showInfo={false}
                    size="small"
                  />
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>

      {/* Detailed Stats */}
      {adminStats && (
        <Row gutter={16}>
          <Col xs={24} lg={8}>
            <Card title="Database Stats" size="small">
              <p>Total Jobs: {adminStats.database?.total_jobs || 0}</p>
              <p>Active Jobs: {adminStats.database?.active_jobs || 0}</p>
              <p>Job Postings: {adminStats.database?.table_counts?.job_postings || 0}</p>
              <p>Scraping Sessions: {adminStats.database?.table_counts?.scraping_sessions || 0}</p>
            </Card>
          </Col>
          <Col xs={24} lg={8}>
            <Card title="Scraping Performance" size="small">
              <p>Total Sessions: {adminStats.scraping?.total_sessions || 0}</p>
              <p>Success Rate: {adminStats.scraping?.success_rate?.toFixed(1) || 0}%</p>
              <p>Successful: {adminStats.scraping?.successful_sessions || 0}</p>
              <p>Failed: {adminStats.scraping?.failed_sessions || 0}</p>
            </Card>
          </Col>
          <Col xs={24} lg={8}>
            <Card title="Recent Activity" size="small">
              <p>Jobs (24h): {adminStats.recent_activity?.jobs_last_24h || 0}</p>
              <p>Sessions (24h): {adminStats.recent_activity?.sessions_last_24h || 0}</p>
              <p>Errors (7d): {adminStats.recent_activity?.errors_last_7d || 0}</p>
              <p>Avg Jobs/Session: {adminStats.recent_activity?.avg_jobs_per_session?.toFixed(1) || 0}</p>
            </Card>
          </Col>
        </Row>
      )}
    </div>
  );
};

export default Dashboard;
