from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging

from database.database import get_db
from database.models import JobPosting
from api.schemas import JobResponse, JobListResponse, JobSearchParams

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=JobListResponse)
async def get_jobs(
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0, description="Number of jobs to skip"),
    limit: int = Query(50, ge=1, le=100, description="Number of jobs to return"),
    search: Optional[str] = Query(None, description="Search in title and description"),
    company: Optional[str] = Query(None, description="Filter by company name"),
    location: Optional[str] = Query(None, description="Filter by location"),
    source: Optional[str] = Query(None, description="Filter by source (indeed, monster, glassdoor)"),
    job_type: Optional[str] = Query(None, description="Filter by job type"),
    is_remote: Optional[bool] = Query(None, description="Filter remote jobs"),
    min_salary: Optional[float] = Query(None, description="Minimum salary"),
    max_salary: Optional[float] = Query(None, description="Maximum salary"),
    posted_since: Optional[int] = Query(None, description="Jobs posted within X days"),
    sort_by: str = Query("posted_date", description="Sort by field (posted_date, salary_min, company, title)"),
    sort_order: str = Query("desc", description="Sort order (asc, desc)")
):
    """Get job listings with filtering and pagination."""
    try:
        # Build query
        query = db.query(JobPosting).filter(JobPosting.is_active == True)
        
        # Apply filters
        if search:
            search_filter = or_(
                JobPosting.title.ilike(f"%{search}%"),
                JobPosting.description.ilike(f"%{search}%"),
                JobPosting.company.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        if company:
            query = query.filter(JobPosting.company.ilike(f"%{company}%"))
        
        if location:
            query = query.filter(JobPosting.location.ilike(f"%{location}%"))
        
        if source:
            query = query.filter(JobPosting.source == source)
        
        if job_type:
            query = query.filter(JobPosting.job_type.ilike(f"%{job_type}%"))
        
        if is_remote is not None:
            query = query.filter(JobPosting.is_remote == is_remote)
        
        if min_salary is not None:
            query = query.filter(JobPosting.salary_min >= min_salary)
        
        if max_salary is not None:
            query = query.filter(JobPosting.salary_max <= max_salary)
        
        if posted_since is not None:
            since_date = datetime.utcnow() - timedelta(days=posted_since)
            query = query.filter(JobPosting.posted_date >= since_date)
        
        # Apply sorting
        sort_column = getattr(JobPosting, sort_by, JobPosting.posted_date)
        if sort_order.lower() == "desc":
            query = query.order_by(desc(sort_column))
        else:
            query = query.order_by(asc(sort_column))
        
        # Get total count before pagination
        total = query.count()
        
        # Apply pagination
        jobs = query.offset(skip).limit(limit).all()
        
        return JobListResponse(
            jobs=[JobResponse.from_orm(job) for job in jobs],
            total=total,
            skip=skip,
            limit=limit,
            has_more=skip + limit < total
        )
        
    except Exception as e:
        logger.error(f"Error getting jobs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{job_id}", response_model=JobResponse)
async def get_job(job_id: str, db: Session = Depends(get_db)):
    """Get a specific job by ID."""
    try:
        job = db.query(JobPosting).filter(JobPosting.id == job_id).first()
        
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        return JobResponse.from_orm(job)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job {job_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/search/suggestions")
async def get_search_suggestions(
    db: Session = Depends(get_db),
    type: str = Query("title", description="Suggestion type (title, company, location)")
):
    """Get search suggestions for autocomplete."""
    try:
        if type == "title":
            # Get most common job titles
            results = db.query(JobPosting.title).filter(
                JobPosting.is_active == True,
                JobPosting.title.isnot(None)
            ).distinct().limit(20).all()
            suggestions = [title for (title,) in results]
            
        elif type == "company":
            # Get most common companies
            results = db.query(JobPosting.company).filter(
                JobPosting.is_active == True,
                JobPosting.company.isnot(None)
            ).distinct().limit(20).all()
            suggestions = [company for (company,) in results]
            
        elif type == "location":
            # Get most common locations
            results = db.query(JobPosting.location).filter(
                JobPosting.is_active == True,
                JobPosting.location.isnot(None)
            ).distinct().limit(20).all()
            suggestions = [location for (location,) in results]
            
        else:
            raise HTTPException(status_code=400, detail="Invalid suggestion type")
        
        return {"suggestions": suggestions}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting suggestions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats/overview")
async def get_job_stats(db: Session = Depends(get_db)):
    """Get job statistics overview."""
    try:
        # Total jobs
        total_jobs = db.query(JobPosting).filter(JobPosting.is_active == True).count()
        
        # Jobs by source
        sources = db.query(
            JobPosting.source,
            db.func.count(JobPosting.id).label('count')
        ).filter(JobPosting.is_active == True).group_by(JobPosting.source).all()
        
        jobs_by_source = {source: count for source, count in sources}
        
        # Jobs by type
        job_types = db.query(
            JobPosting.job_type,
            db.func.count(JobPosting.id).label('count')
        ).filter(
            JobPosting.is_active == True,
            JobPosting.job_type.isnot(None)
        ).group_by(JobPosting.job_type).all()
        
        jobs_by_type = {job_type: count for job_type, count in job_types}
        
        # Remote vs on-site
        remote_jobs = db.query(JobPosting).filter(
            JobPosting.is_active == True,
            JobPosting.is_remote == True
        ).count()
        
        # Recent activity (last 7 days)
        week_ago = datetime.utcnow() - timedelta(days=7)
        recent_jobs = db.query(JobPosting).filter(
            JobPosting.scraped_at >= week_ago
        ).count()
        
        # Top companies (by job count)
        top_companies = db.query(
            JobPosting.company,
            db.func.count(JobPosting.id).label('count')
        ).filter(
            JobPosting.is_active == True,
            JobPosting.company.isnot(None)
        ).group_by(JobPosting.company).order_by(
            db.func.count(JobPosting.id).desc()
        ).limit(10).all()
        
        # Top locations
        top_locations = db.query(
            JobPosting.location,
            db.func.count(JobPosting.id).label('count')
        ).filter(
            JobPosting.is_active == True,
            JobPosting.location.isnot(None)
        ).group_by(JobPosting.location).order_by(
            db.func.count(JobPosting.id).desc()
        ).limit(10).all()
        
        # Salary statistics
        salary_stats = db.query(
            db.func.avg(JobPosting.salary_min).label('avg_min'),
            db.func.avg(JobPosting.salary_max).label('avg_max'),
            db.func.min(JobPosting.salary_min).label('min_salary'),
            db.func.max(JobPosting.salary_max).label('max_salary')
        ).filter(
            JobPosting.is_active == True,
            JobPosting.salary_min.isnot(None)
        ).first()
        
        stats = {
            "total_jobs": total_jobs,
            "jobs_by_source": jobs_by_source,
            "jobs_by_type": jobs_by_type,
            "remote_jobs": remote_jobs,
            "recent_jobs_7d": recent_jobs,
            "top_companies": [
                {"company": company, "job_count": count}
                for company, count in top_companies
            ],
            "top_locations": [
                {"location": location, "job_count": count}
                for location, count in top_locations
            ],
            "salary_stats": {
                "avg_min_salary": float(salary_stats.avg_min) if salary_stats.avg_min else None,
                "avg_max_salary": float(salary_stats.avg_max) if salary_stats.avg_max else None,
                "min_salary": float(salary_stats.min_salary) if salary_stats.min_salary else None,
                "max_salary": float(salary_stats.max_salary) if salary_stats.max_salary else None,
            } if salary_stats else None
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting job stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search/advanced", response_model=JobListResponse)
async def advanced_search(
    search_params: JobSearchParams,
    db: Session = Depends(get_db)
):
    """Advanced job search with complex filtering."""
    try:
        query = db.query(JobPosting).filter(JobPosting.is_active == True)
        
        # Apply all filters from search params
        if search_params.keywords:
            keyword_filters = []
            for keyword in search_params.keywords:
                keyword_filter = or_(
                    JobPosting.title.ilike(f"%{keyword}%"),
                    JobPosting.description.ilike(f"%{keyword}%")
                )
                keyword_filters.append(keyword_filter)
            
            if search_params.keyword_match_all:
                query = query.filter(and_(*keyword_filters))
            else:
                query = query.filter(or_(*keyword_filters))
        
        if search_params.companies:
            company_filter = or_(*[
                JobPosting.company.ilike(f"%{company}%") 
                for company in search_params.companies
            ])
            query = query.filter(company_filter)
        
        if search_params.locations:
            location_filter = or_(*[
                JobPosting.location.ilike(f"%{location}%") 
                for location in search_params.locations
            ])
            query = query.filter(location_filter)
        
        if search_params.sources:
            query = query.filter(JobPosting.source.in_(search_params.sources))
        
        if search_params.job_types:
            job_type_filter = or_(*[
                JobPosting.job_type.ilike(f"%{job_type}%") 
                for job_type in search_params.job_types
            ])
            query = query.filter(job_type_filter)
        
        if search_params.is_remote is not None:
            query = query.filter(JobPosting.is_remote == search_params.is_remote)
        
        if search_params.salary_range:
            if search_params.salary_range.min_salary:
                query = query.filter(JobPosting.salary_min >= search_params.salary_range.min_salary)
            if search_params.salary_range.max_salary:
                query = query.filter(JobPosting.salary_max <= search_params.salary_range.max_salary)
        
        if search_params.date_range:
            if search_params.date_range.start_date:
                query = query.filter(JobPosting.posted_date >= search_params.date_range.start_date)
            if search_params.date_range.end_date:
                query = query.filter(JobPosting.posted_date <= search_params.date_range.end_date)
        
        # Apply sorting
        sort_column = getattr(JobPosting, search_params.sort_by, JobPosting.posted_date)
        if search_params.sort_order.lower() == "desc":
            query = query.order_by(desc(sort_column))
        else:
            query = query.order_by(asc(sort_column))
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        jobs = query.offset(search_params.skip).limit(search_params.limit).all()
        
        return JobListResponse(
            jobs=[JobResponse.from_orm(job) for job in jobs],
            total=total,
            skip=search_params.skip,
            limit=search_params.limit,
            has_more=search_params.skip + search_params.limit < total
        )
        
    except Exception as e:
        logger.error(f"Error in advanced search: {e}")
        raise HTTPException(status_code=500, detail=str(e))
