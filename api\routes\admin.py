from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import desc, func
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging

from database.database import get_db, DatabaseManager
from database.models import JobPosting, ScrapingSession, ProxyStatus
from api.schemas import (
    ScrapingSessionResponse, ScrapingSessionListResponse, ProxyStatusResponse,
    AdminStatsResponse, TriggerScrapingRequest, TaskStatusResponse,
    BulkJobActionRequest, JobUpdateRequest, SuccessResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/stats", response_model=AdminStatsResponse)
async def get_admin_stats(db: Session = Depends(get_db)):
    """Get comprehensive admin statistics."""
    try:
        # Database stats
        table_counts = DatabaseManager.get_table_counts()
        
        # Job statistics
        total_jobs = db.query(JobPosting).count()
        active_jobs = db.query(JobPosting).filter(JobPosting.is_active == True).count()
        
        # Recent activity (last 24 hours)
        yesterday = datetime.utcnow() - timedelta(days=1)
        recent_jobs = db.query(JobPosting).filter(JobPosting.scraped_at >= yesterday).count()
        recent_sessions = db.query(ScrapingSession).filter(ScrapingSession.started_at >= yesterday).count()
        
        # Scraping statistics
        total_sessions = db.query(ScrapingSession).count()
        successful_sessions = db.query(ScrapingSession).filter(ScrapingSession.status == 'completed').count()
        failed_sessions = db.query(ScrapingSession).filter(ScrapingSession.status == 'failed').count()
        
        # Source breakdown
        source_stats = db.query(
            JobPosting.source,
            func.count(JobPosting.id).label('count'),
            func.avg(func.extract('epoch', JobPosting.updated_at - JobPosting.scraped_at)).label('avg_processing_time')
        ).group_by(JobPosting.source).all()
        
        # Error analysis
        error_sessions = db.query(ScrapingSession).filter(
            ScrapingSession.errors_count > 0,
            ScrapingSession.started_at >= datetime.utcnow() - timedelta(days=7)
        ).all()
        
        # Proxy statistics
        proxy_stats = {}
        if db.query(ProxyStatus).count() > 0:
            active_proxies = db.query(ProxyStatus).filter(ProxyStatus.is_active == True).count()
            avg_success_rate = db.query(func.avg(ProxyStatus.success_rate)).filter(ProxyStatus.is_active == True).scalar()
            proxy_stats = {
                "total_proxies": db.query(ProxyStatus).count(),
                "active_proxies": active_proxies,
                "avg_success_rate": float(avg_success_rate) if avg_success_rate else 0.0
            }
        
        stats = AdminStatsResponse(
            database={
                "table_counts": table_counts,
                "total_jobs": total_jobs,
                "active_jobs": active_jobs,
                "database_healthy": DatabaseManager.health_check()
            },
            scraping={
                "total_sessions": total_sessions,
                "successful_sessions": successful_sessions,
                "failed_sessions": failed_sessions,
                "success_rate": (successful_sessions / total_sessions * 100) if total_sessions > 0 else 0,
                "source_breakdown": [
                    {
                        "source": source,
                        "job_count": count,
                        "avg_processing_time": float(avg_time) if avg_time else 0
                    }
                    for source, count, avg_time in source_stats
                ],
                "proxy_stats": proxy_stats
            },
            system={
                "uptime": "N/A",  # Would need to track application start time
                "memory_usage": "N/A",  # Would need psutil
                "cpu_usage": "N/A"
            },
            recent_activity={
                "jobs_last_24h": recent_jobs,
                "sessions_last_24h": recent_sessions,
                "errors_last_7d": len(error_sessions),
                "avg_jobs_per_session": recent_jobs / recent_sessions if recent_sessions > 0 else 0
            }
        )
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting admin stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sessions", response_model=ScrapingSessionListResponse)
async def get_scraping_sessions(
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    source: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    days: int = Query(7, description="Number of days to look back")
):
    """Get scraping sessions with filtering."""
    try:
        query = db.query(ScrapingSession)
        
        # Filter by date range
        since_date = datetime.utcnow() - timedelta(days=days)
        query = query.filter(ScrapingSession.started_at >= since_date)
        
        # Apply filters
        if source:
            query = query.filter(ScrapingSession.source == source)
        
        if status:
            query = query.filter(ScrapingSession.status == status)
        
        # Order by most recent first
        query = query.order_by(desc(ScrapingSession.started_at))
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        sessions = query.offset(skip).limit(limit).all()
        
        return ScrapingSessionListResponse(
            sessions=[ScrapingSessionResponse.from_orm(session) for session in sessions],
            total=total,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"Error getting scraping sessions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/proxies", response_model=List[ProxyStatusResponse])
async def get_proxy_status(db: Session = Depends(get_db)):
    """Get proxy status information."""
    try:
        proxies = db.query(ProxyStatus).order_by(desc(ProxyStatus.success_rate)).all()
        return [ProxyStatusResponse.from_orm(proxy) for proxy in proxies]
        
    except Exception as e:
        logger.error(f"Error getting proxy status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/scraping/trigger")
async def trigger_scraping_admin(
    request: TriggerScrapingRequest,
    background_tasks: BackgroundTasks
):
    """Trigger scraping with admin controls."""
    try:
        from scheduler.tasks import scrape_all_sources, scrape_indeed, scrape_monster, scrape_glassdoor
        
        if not request.sources:
            # Scrape all enabled sources
            task = scrape_all_sources.delay(request.search_terms, request.locations, request.max_pages)
            task_id = task.id
        else:
            # Scrape specific sources
            tasks = []
            for source in request.sources:
                if source == "indeed":
                    task = scrape_indeed.delay(request.search_terms, request.locations, request.max_pages)
                elif source == "monster":
                    task = scrape_monster.delay(request.search_terms, request.locations, request.max_pages)
                elif source == "glassdoor":
                    task = scrape_glassdoor.delay(request.search_terms, request.locations, request.max_pages)
                else:
                    raise HTTPException(status_code=400, detail=f"Unknown source: {source}")
                tasks.append(task.id)
            
            task_id = tasks[0] if len(tasks) == 1 else tasks
        
        return {
            "message": "Scraping triggered successfully",
            "task_id": task_id,
            "sources": request.sources or ["all"],
            "parameters": request.dict()
        }
        
    except Exception as e:
        logger.error(f"Error triggering scraping: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}", response_model=TaskStatusResponse)
async def get_task_status_admin(task_id: str):
    """Get detailed task status for admin."""
    try:
        from celery.result import AsyncResult
        from scheduler.celery_app import app as celery_app
        
        result = AsyncResult(task_id, app=celery_app)
        
        response = TaskStatusResponse(
            task_id=task_id,
            status=result.status,
            result=result.result if result.ready() else None
        )
        
        if result.status == 'PROGRESS':
            response.progress = result.info
        elif result.status == 'FAILURE':
            response.error = str(result.info)
        
        return response
        
    except Exception as e:
        logger.error(f"Error getting task status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/jobs/bulk-action")
async def bulk_job_action(
    request: BulkJobActionRequest,
    db: Session = Depends(get_db)
):
    """Perform bulk actions on job postings."""
    try:
        jobs = db.query(JobPosting).filter(JobPosting.id.in_(request.job_ids)).all()
        
        if not jobs:
            raise HTTPException(status_code=404, detail="No jobs found with provided IDs")
        
        affected_count = 0
        
        if request.action == "activate":
            for job in jobs:
                job.is_active = True
                affected_count += 1
        elif request.action == "deactivate":
            for job in jobs:
                job.is_active = False
                affected_count += 1
        elif request.action == "delete":
            for job in jobs:
                db.delete(job)
                affected_count += 1
        else:
            raise HTTPException(status_code=400, detail=f"Unknown action: {request.action}")
        
        db.commit()
        
        return SuccessResponse(
            message=f"Successfully {request.action}d {affected_count} jobs",
            data={"affected_count": affected_count, "action": request.action},
            timestamp=datetime.utcnow().isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error performing bulk action: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/jobs/{job_id}")
async def update_job(
    job_id: str,
    request: JobUpdateRequest,
    db: Session = Depends(get_db)
):
    """Update a specific job posting."""
    try:
        job = db.query(JobPosting).filter(JobPosting.id == job_id).first()
        
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        # Update fields that are provided
        update_data = request.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(job, field):
                setattr(job, field, value)
        
        job.updated_at = datetime.utcnow()
        db.commit()
        
        return SuccessResponse(
            message="Job updated successfully",
            data={"job_id": str(job.id), "updated_fields": list(update_data.keys())},
            timestamp=datetime.utcnow().isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating job {job_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/maintenance/cleanup")
async def trigger_cleanup(
    days: int = Query(30, description="Delete data older than X days"),
    background_tasks: BackgroundTasks
):
    """Trigger database cleanup."""
    try:
        from scheduler.tasks import cleanup_old_data
        
        task = cleanup_old_data.delay(days)
        
        return {
            "message": f"Cleanup triggered for data older than {days} days",
            "task_id": task.id
        }
        
    except Exception as e:
        logger.error(f"Error triggering cleanup: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/logs/errors")
async def get_recent_errors(
    db: Session = Depends(get_db),
    hours: int = Query(24, description="Hours to look back"),
    limit: int = Query(50, ge=1, le=200)
):
    """Get recent scraping errors."""
    try:
        since_time = datetime.utcnow() - timedelta(hours=hours)
        
        error_sessions = db.query(ScrapingSession).filter(
            ScrapingSession.started_at >= since_time,
            ScrapingSession.errors_count > 0
        ).order_by(desc(ScrapingSession.started_at)).limit(limit).all()
        
        errors = []
        for session in error_sessions:
            errors.append({
                "session_id": str(session.id),
                "source": session.source,
                "started_at": session.started_at.isoformat(),
                "error_count": session.errors_count,
                "error_details": session.error_details,
                "status": session.status
            })
        
        return {
            "errors": errors,
            "total_error_sessions": len(errors),
            "time_range_hours": hours
        }
        
    except Exception as e:
        logger.error(f"Error getting recent errors: {e}")
        raise HTTPException(status_code=500, detail=str(e))
