import random
from typing import List
from fake_useragent import UserAgent
import logging

from config.settings import USER_AGENTS

logger = logging.getLogger(__name__)


class UserAgentManager:
    """Manages user agent rotation for web scraping."""
    
    def __init__(self):
        self.static_agents = USER_AGENTS
        self.ua_generator = None
        self.current_agent = None
        self.use_fake_useragent = True
        
        # Try to initialize fake-useragent
        try:
            self.ua_generator = UserAgent()
            logger.info("Initialized fake-useragent for dynamic user agents")
        except Exception as e:
            logger.warning(f"Failed to initialize fake-useragent: {e}. Using static list.")
            self.use_fake_useragent = False
    
    def get_random_user_agent(self) -> str:
        """Get a random user agent string."""
        try:
            if self.use_fake_useragent and self.ua_generator:
                # Try to get a random Chrome user agent
                agent = self.ua_generator.chrome
                self.current_agent = agent
                return agent
        except Exception as e:
            logger.debug(f"Error getting dynamic user agent: {e}")
        
        # Fallback to static list
        agent = random.choice(self.static_agents)
        self.current_agent = agent
        return agent
    
    def get_chrome_user_agent(self) -> str:
        """Get a Chrome-specific user agent."""
        try:
            if self.use_fake_useragent and self.ua_generator:
                return self.ua_generator.chrome
        except Exception:
            pass
        
        # Fallback to Chrome agents from static list
        chrome_agents = [ua for ua in self.static_agents if 'Chrome' in ua]
        return random.choice(chrome_agents) if chrome_agents else self.static_agents[0]
    
    def get_firefox_user_agent(self) -> str:
        """Get a Firefox-specific user agent."""
        try:
            if self.use_fake_useragent and self.ua_generator:
                return self.ua_generator.firefox
        except Exception:
            pass
        
        # Fallback to Firefox agents from static list
        firefox_agents = [ua for ua in self.static_agents if 'Firefox' in ua]
        return random.choice(firefox_agents) if firefox_agents else self.static_agents[-1]
    
    def get_mobile_user_agent(self) -> str:
        """Get a mobile user agent."""
        mobile_agents = [
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
            'Mozilla/5.0 (Linux; Android 11; Pixel 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
        ]
        return random.choice(mobile_agents)
    
    def get_headers_with_user_agent(self, user_agent: str = None) -> dict:
        """Get complete headers with user agent and other common headers."""
        if not user_agent:
            user_agent = self.get_random_user_agent()
        
        headers = {
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0',
        }
        
        # Add random variations to make requests look more natural
        if random.choice([True, False]):
            headers['Accept-Language'] = random.choice([
                'en-US,en;q=0.9',
                'en-GB,en;q=0.9',
                'en-US,en;q=0.8,es;q=0.6',
            ])
        
        return headers
    
    def get_selenium_user_agent(self) -> str:
        """Get a user agent suitable for Selenium (avoid detection)."""
        # Use agents that are less likely to be detected as automated
        selenium_safe_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
        ]
        return random.choice(selenium_safe_agents)
    
    def rotate_user_agent(self) -> str:
        """Rotate to a new user agent and return it."""
        new_agent = self.get_random_user_agent()
        logger.debug(f"Rotated user agent to: {new_agent[:50]}...")
        return new_agent


# Global user agent manager instance
user_agent_manager = UserAgentManager()


# Convenience functions
def get_random_headers() -> dict:
    """Get random headers for requests."""
    return user_agent_manager.get_headers_with_user_agent()


def get_chrome_headers() -> dict:
    """Get Chrome-specific headers."""
    return user_agent_manager.get_headers_with_user_agent(
        user_agent_manager.get_chrome_user_agent()
    )


def get_firefox_headers() -> dict:
    """Get Firefox-specific headers."""
    return user_agent_manager.get_headers_with_user_agent(
        user_agent_manager.get_firefox_user_agent()
    )
