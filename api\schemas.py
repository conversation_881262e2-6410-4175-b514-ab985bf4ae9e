from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID


class JobResponse(BaseModel):
    """Response model for job postings."""
    
    id: UUID
    title: str
    company: str
    location: Optional[str] = None
    description: Optional[str] = None
    job_type: Optional[str] = None
    experience_level: Optional[str] = None
    salary_min: Optional[float] = None
    salary_max: Optional[float] = None
    salary_currency: Optional[str] = None
    salary_period: Optional[str] = None
    source: str
    source_url: str
    source_job_id: Optional[str] = None
    posted_date: Optional[datetime] = None
    scraped_at: datetime
    updated_at: datetime
    is_active: bool
    is_remote: bool
    skills_required: Optional[str] = None
    benefits: Optional[str] = None
    quality_score: Optional[float] = None
    
    class Config:
        from_attributes = True


class JobListResponse(BaseModel):
    """Response model for job listings with pagination."""
    
    jobs: List[JobResponse]
    total: int
    skip: int
    limit: int
    has_more: bool


class SalaryRange(BaseModel):
    """Salary range filter."""
    
    min_salary: Optional[float] = None
    max_salary: Optional[float] = None


class DateRange(BaseModel):
    """Date range filter."""
    
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None


class JobSearchParams(BaseModel):
    """Advanced job search parameters."""
    
    keywords: Optional[List[str]] = None
    keyword_match_all: bool = False  # True for AND, False for OR
    companies: Optional[List[str]] = None
    locations: Optional[List[str]] = None
    sources: Optional[List[str]] = None
    job_types: Optional[List[str]] = None
    is_remote: Optional[bool] = None
    salary_range: Optional[SalaryRange] = None
    date_range: Optional[DateRange] = None
    skip: int = Field(0, ge=0)
    limit: int = Field(50, ge=1, le=100)
    sort_by: str = Field("posted_date", description="Field to sort by")
    sort_order: str = Field("desc", description="Sort order (asc/desc)")


class ScrapingSessionResponse(BaseModel):
    """Response model for scraping sessions."""
    
    id: UUID
    source: str
    started_at: datetime
    completed_at: Optional[datetime] = None
    status: str
    jobs_found: int
    jobs_new: int
    jobs_updated: int
    jobs_skipped: int
    errors_count: int
    error_details: Optional[str] = None
    pages_scraped: int
    requests_made: int
    avg_response_time: Optional[float] = None
    
    class Config:
        from_attributes = True


class ScrapingSessionListResponse(BaseModel):
    """Response model for scraping session listings."""
    
    sessions: List[ScrapingSessionResponse]
    total: int
    skip: int
    limit: int


class ProxyStatusResponse(BaseModel):
    """Response model for proxy status."""
    
    id: UUID
    proxy_url: str
    is_active: bool
    success_rate: float
    avg_response_time: Optional[float] = None
    total_requests: int
    failed_requests: int
    last_used: Optional[datetime] = None
    last_success: Optional[datetime] = None
    last_failure: Optional[datetime] = None
    failure_reason: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class TriggerScrapingRequest(BaseModel):
    """Request model for triggering scraping."""
    
    sources: Optional[List[str]] = None
    search_terms: List[str] = Field(default=["software engineer"])
    locations: List[str] = Field(default=["New York"])
    max_pages: int = Field(default=5, ge=1, le=20)


class TaskStatusResponse(BaseModel):
    """Response model for Celery task status."""
    
    task_id: str
    status: str
    result: Optional[Dict[str, Any]] = None
    progress: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class HealthCheckResponse(BaseModel):
    """Response model for health check."""
    
    status: str
    timestamp: str
    components: Dict[str, str]
    metrics: Optional[Dict[str, Any]] = None


class MetricsResponse(BaseModel):
    """Response model for application metrics."""
    
    timestamp: str
    jobs: Dict[str, Any]
    scraping: Dict[str, Any]
    top_locations: List[Dict[str, Any]]


class AdminStatsResponse(BaseModel):
    """Response model for admin statistics."""
    
    database: Dict[str, Any]
    scraping: Dict[str, Any]
    system: Dict[str, Any]
    recent_activity: Dict[str, Any]


class ConfigUpdateRequest(BaseModel):
    """Request model for updating configuration."""
    
    scraping_delay_min: Optional[int] = None
    scraping_delay_max: Optional[int] = None
    max_concurrent_requests: Optional[int] = None
    proxy_enabled: Optional[bool] = None
    captcha_enabled: Optional[bool] = None
    indeed_enabled: Optional[bool] = None
    monster_enabled: Optional[bool] = None
    glassdoor_enabled: Optional[bool] = None


class BulkJobActionRequest(BaseModel):
    """Request model for bulk job actions."""
    
    job_ids: List[UUID]
    action: str  # 'activate', 'deactivate', 'delete'


class JobUpdateRequest(BaseModel):
    """Request model for updating job postings."""
    
    title: Optional[str] = None
    company: Optional[str] = None
    location: Optional[str] = None
    description: Optional[str] = None
    job_type: Optional[str] = None
    experience_level: Optional[str] = None
    salary_min: Optional[float] = None
    salary_max: Optional[float] = None
    is_active: Optional[bool] = None
    is_remote: Optional[bool] = None
    skills_required: Optional[str] = None
    benefits: Optional[str] = None


class ErrorResponse(BaseModel):
    """Standard error response model."""
    
    error: str
    detail: Optional[str] = None
    timestamp: str


class SuccessResponse(BaseModel):
    """Standard success response model."""
    
    message: str
    data: Optional[Dict[str, Any]] = None
    timestamp: str
