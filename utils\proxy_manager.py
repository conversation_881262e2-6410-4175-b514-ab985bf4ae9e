import random
import requests
import time
from typing import List, Optional, Dict
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging

from config.settings import settings
from database.database import get_db_session
from database.models import ProxyStatus

logger = logging.getLogger(__name__)


@dataclass
class Proxy:
    """Proxy configuration data class."""
    url: str
    username: Optional[str] = None
    password: Optional[str] = None
    protocol: str = "http"
    
    @property
    def proxy_dict(self) -> Dict[str, str]:
        """Get proxy configuration for requests."""
        if self.username and self.password:
            auth_url = f"{self.protocol}://{self.username}:{self.password}@{self.url}"
        else:
            auth_url = f"{self.protocol}://{self.url}"
        
        return {
            "http": auth_url,
            "https": auth_url
        }


class ProxyManager:
    """Manages proxy rotation and health monitoring."""
    
    def __init__(self):
        self.proxies: List[Proxy] = []
        self.current_index = 0
        self.last_rotation = datetime.utcnow()
        self.rotation_interval = timedelta(minutes=5)  # Rotate every 5 minutes
        self.load_proxies()
    
    def load_proxies(self):
        """Load proxies from configuration or external source."""
        if not settings.proxy_enabled:
            logger.info("Proxy rotation disabled")
            return
        
        # Load from database first
        self._load_from_database()
        
        # If no proxies in database, try to load from external source
        if not self.proxies and settings.proxy_list_url:
            self._load_from_url()
        
        # Fallback to default proxy if configured
        if not self.proxies and settings.proxy_username:
            default_proxy = Proxy(
                url="proxy.example.com:8080",  # Replace with actual proxy
                username=settings.proxy_username,
                password=settings.proxy_password
            )
            self.proxies.append(default_proxy)
        
        logger.info(f"Loaded {len(self.proxies)} proxies")
    
    def _load_from_database(self):
        """Load active proxies from database."""
        try:
            with get_db_session() as session:
                proxy_records = session.query(ProxyStatus).filter(
                    ProxyStatus.is_active == True,
                    ProxyStatus.success_rate > 0.5  # Only use proxies with >50% success rate
                ).all()
                
                for record in proxy_records:
                    proxy = Proxy(url=record.proxy_url)
                    self.proxies.append(proxy)
                    
        except Exception as e:
            logger.error(f"Error loading proxies from database: {e}")
    
    def _load_from_url(self):
        """Load proxies from external URL."""
        try:
            response = requests.get(settings.proxy_list_url, timeout=10)
            response.raise_for_status()
            
            # Assuming the URL returns one proxy per line in format: ip:port
            for line in response.text.strip().split('\n'):
                if ':' in line:
                    proxy = Proxy(url=line.strip())
                    self.proxies.append(proxy)
                    
        except Exception as e:
            logger.error(f"Error loading proxies from URL: {e}")
    
    def get_proxy(self) -> Optional[Proxy]:
        """Get the next proxy in rotation."""
        if not self.proxies:
            return None
        
        # Check if it's time to rotate
        if datetime.utcnow() - self.last_rotation > self.rotation_interval:
            self.rotate_proxy()
        
        return self.proxies[self.current_index]
    
    def rotate_proxy(self):
        """Rotate to the next proxy."""
        if not self.proxies:
            return
        
        self.current_index = (self.current_index + 1) % len(self.proxies)
        self.last_rotation = datetime.utcnow()
        logger.debug(f"Rotated to proxy {self.current_index}: {self.proxies[self.current_index].url}")
    
    def mark_proxy_success(self, proxy: Proxy, response_time: float):
        """Mark a proxy as successful and update its statistics."""
        self._update_proxy_stats(proxy, success=True, response_time=response_time)
    
    def mark_proxy_failure(self, proxy: Proxy, error: str):
        """Mark a proxy as failed and update its statistics."""
        self._update_proxy_stats(proxy, success=False, error=error)
    
    def _update_proxy_stats(self, proxy: Proxy, success: bool, response_time: Optional[float] = None, error: Optional[str] = None):
        """Update proxy statistics in database."""
        try:
            with get_db_session() as session:
                proxy_record = session.query(ProxyStatus).filter(
                    ProxyStatus.proxy_url == proxy.url
                ).first()
                
                if not proxy_record:
                    proxy_record = ProxyStatus(proxy_url=proxy.url)
                    session.add(proxy_record)
                
                # Update statistics
                proxy_record.total_requests += 1
                proxy_record.last_used = datetime.utcnow()
                
                if success:
                    proxy_record.last_success = datetime.utcnow()
                    if response_time:
                        # Update average response time
                        if proxy_record.avg_response_time:
                            proxy_record.avg_response_time = (proxy_record.avg_response_time + response_time) / 2
                        else:
                            proxy_record.avg_response_time = response_time
                else:
                    proxy_record.failed_requests += 1
                    proxy_record.last_failure = datetime.utcnow()
                    if error:
                        proxy_record.failure_reason = error[:200]  # Truncate long errors
                
                # Calculate success rate
                if proxy_record.total_requests > 0:
                    success_requests = proxy_record.total_requests - proxy_record.failed_requests
                    proxy_record.success_rate = success_requests / proxy_record.total_requests
                
                # Deactivate proxy if success rate is too low
                if proxy_record.total_requests >= 10 and proxy_record.success_rate < 0.3:
                    proxy_record.is_active = False
                    logger.warning(f"Deactivated proxy {proxy.url} due to low success rate: {proxy_record.success_rate}")
                
        except Exception as e:
            logger.error(f"Error updating proxy stats: {e}")
    
    def test_proxy(self, proxy: Proxy, test_url: str = "http://httpbin.org/ip") -> bool:
        """Test if a proxy is working."""
        try:
            start_time = time.time()
            response = requests.get(test_url, proxies=proxy.proxy_dict, timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                self.mark_proxy_success(proxy, response_time)
                return True
            else:
                self.mark_proxy_failure(proxy, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.mark_proxy_failure(proxy, str(e))
            return False
    
    def get_healthy_proxies(self) -> List[Proxy]:
        """Get list of healthy proxies."""
        healthy = []
        for proxy in self.proxies:
            if self.test_proxy(proxy):
                healthy.append(proxy)
        return healthy


# Global proxy manager instance
proxy_manager = ProxyManager()
