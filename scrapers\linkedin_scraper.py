import time
import random
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from urllib.parse import urlencode, urljoin, quote
import logging
import re

from bs4 import BeautifulSoup
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.keys import Keys

from scrapers.base_scraper import BaseScraper

logger = logging.getLogger(__name__)


class LinkedInScraper(BaseScraper):
    """
    Scraper for LinkedIn job postings.
    Note: LinkedIn has strict anti-bot measures. Use with caution and respect rate limits.
    Consider using LinkedIn's official API for production use.
    """
    
    def __init__(self):
        super().__init__("linkedin")
        self.base_url = "https://www.linkedin.com"
        self.search_url = "https://www.linkedin.com/jobs/search"
        self.use_selenium = True  # LinkedIn requires JavaScript
        self.login_required = False  # Can scrape public job listings without login
    
    def scrape_jobs(self, search_terms: List[str], locations: List[str], max_pages: int = 5) -> int:
        """Scrape jobs from LinkedIn using Selenium."""
        total_jobs = 0
        
        # Initialize Selenium driver
        driver = self._get_selenium_driver()
        
        try:
            for search_term in search_terms:
                for location in locations:
                    logger.info(f"Scraping LinkedIn for '{search_term}' in '{location}'")
                    jobs_found = self._scrape_search_results_selenium(driver, search_term, location, max_pages)
                    total_jobs += jobs_found
                    
                    # Add longer delay between different search combinations for LinkedIn
                    time.sleep(random.uniform(15, 25))
        
        finally:
            if driver:
                driver.quit()
        
        return total_jobs
    
    def _scrape_search_results_selenium(self, driver, search_term: str, location: str, max_pages: int) -> int:
        """Scrape search results using Selenium."""
        jobs_found = 0
        
        try:
            # Build search URL
            params = {
                'keywords': search_term,
                'location': location,
                'f_TPR': 'r86400',  # Posted in last 24 hours
                'f_JT': 'F',  # Full-time jobs
                'sortBy': 'DD',  # Sort by date
                'redirect': 'false',
                'position': 1,
                'pageNum': 0
            }
            
            search_url = f"{self.search_url}?{urlencode(params)}"
            logger.debug(f"Navigating to: {search_url}")
            
            driver.get(search_url)
            time.sleep(random.uniform(5, 8))
            
            # Handle potential popups/modals
            self._handle_linkedin_popups(driver)
            
            for page in range(max_pages):
                try:
                    logger.debug(f"Scraping page {page + 1}")
                    
                    # Wait for job listings to load
                    WebDriverWait(driver, 15).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, '.job-search-card'))
                    )
                    
                    # Scroll to load more jobs (LinkedIn uses infinite scroll)
                    self._scroll_to_load_jobs(driver)
                    
                    # Get job listings
                    job_elements = driver.find_elements(By.CSS_SELECTOR, '.job-search-card')
                    
                    if not job_elements:
                        logger.warning(f"No job listings found on page {page + 1}")
                        break
                    
                    # Parse each job listing
                    for job_element in job_elements:
                        try:
                            job_data = self.parse_job_listing_selenium(job_element)
                            if job_data:
                                if self._save_job(job_data):
                                    jobs_found += 1
                        except Exception as e:
                            logger.error(f"Error parsing job element: {e}")
                            continue
                    
                    self.stats['pages_scraped'] += 1
                    
                    # Try to go to next page
                    try:
                        next_button = driver.find_element(By.CSS_SELECTOR, 'button[aria-label="View next page"]')
                        if next_button.is_enabled() and next_button.is_displayed():
                            driver.execute_script("arguments[0].click();", next_button)
                            time.sleep(random.uniform(5, 8))
                        else:
                            logger.info(f"No more pages available after page {page + 1}")
                            break
                    except NoSuchElementException:
                        logger.info(f"No next button found after page {page + 1}")
                        break
                
                except TimeoutException:
                    logger.warning(f"Timeout waiting for page {page + 1} to load")
                    break
                except Exception as e:
                    logger.error(f"Error scraping page {page + 1}: {e}")
                    self.stats['errors'] += 1
                    continue
        
        except Exception as e:
            logger.error(f"Error in LinkedIn Selenium scraping: {e}")
            self.stats['errors'] += 1
        
        return jobs_found
    
    def _handle_linkedin_popups(self, driver):
        """Handle common popups and modals on LinkedIn."""
        try:
            # Close sign-up modal if present
            close_selectors = [
                'button[data-tracking-control-name="public_jobs_contextual-sign-up-modal_modal_dismiss"]',
                '.modal__dismiss',
                '[data-test-modal-close-btn]',
                '.artdeco-modal__dismiss'
            ]
            
            for selector in close_selectors:
                try:
                    close_button = driver.find_element(By.CSS_SELECTOR, selector)
                    if close_button.is_displayed():
                        close_button.click()
                        time.sleep(2)
                        break
                except Exception:
                    continue
            
            # Handle cookie consent
            try:
                cookie_button = driver.find_element(By.CSS_SELECTOR, 'button[data-tracking-control-name="guest-homepage-basic_cookie-consent-banner_accept-all-cookies"]')
                if cookie_button.is_displayed():
                    cookie_button.click()
                    time.sleep(2)
            except Exception:
                pass
                
        except Exception as e:
            logger.debug(f"Error handling LinkedIn popups: {e}")
    
    def _scroll_to_load_jobs(self, driver):
        """Scroll down to trigger loading of more jobs."""
        try:
            # Scroll down gradually to load more jobs
            last_height = driver.execute_script("return document.body.scrollHeight")
            
            for _ in range(3):  # Scroll 3 times
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(random.uniform(2, 4))
                
                new_height = driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    break
                last_height = new_height
            
            # Scroll back to top
            driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)
            
        except Exception as e:
            logger.debug(f"Error scrolling to load jobs: {e}")
    
    def parse_job_listing_selenium(self, job_element) -> Optional[Dict[str, Any]]:
        """Parse a single LinkedIn job listing using Selenium."""
        try:
            job_data = {}
            
            # Title and URL
            try:
                title_element = job_element.find_element(By.CSS_SELECTOR, '.base-search-card__title a')
                job_data['title'] = title_element.text.strip()
                job_data['source_url'] = title_element.get_attribute('href')
            except Exception:
                return None
            
            # Company
            try:
                company_element = job_element.find_element(By.CSS_SELECTOR, '.base-search-card__subtitle a')
                job_data['company'] = company_element.text.strip()
            except Exception:
                try:
                    company_element = job_element.find_element(By.CSS_SELECTOR, '.base-search-card__subtitle')
                    job_data['company'] = company_element.text.strip()
                except Exception:
                    pass
            
            # Location
            try:
                location_element = job_element.find_element(By.CSS_SELECTOR, '.job-search-card__location')
                job_data['location'] = location_element.text.strip()
            except Exception:
                pass
            
            # Job description/snippet
            try:
                description_element = job_element.find_element(By.CSS_SELECTOR, '.job-search-card__snippet')
                job_data['description'] = description_element.text.strip()
            except Exception:
                pass
            
            # Posted date
            try:
                date_element = job_element.find_element(By.CSS_SELECTOR, '.job-search-card__listdate')
                date_text = date_element.text.strip()
                posted_date = self._parse_posted_date(date_text)
                if posted_date:
                    job_data['posted_date'] = posted_date
            except Exception:
                pass
            
            # Check for remote work
            remote_indicators = ['remote', 'work from home', 'telecommute', 'virtual', 'anywhere']
            job_text = f"{job_data.get('title', '')} {job_data.get('description', '')} {job_data.get('location', '')}".lower()
            job_data['is_remote'] = any(indicator in job_text for indicator in remote_indicators)
            
            # Set default values
            job_data.setdefault('job_type', 'full-time')
            job_data.setdefault('is_remote', False)
            job_data.setdefault('salary_currency', 'USD')
            
            return job_data
            
        except Exception as e:
            logger.error(f"Error parsing LinkedIn job listing: {e}")
            return None
    
    def parse_job_listing(self, job_element) -> Optional[Dict[str, Any]]:
        """Parse a single job listing (fallback for BeautifulSoup)."""
        # This is a fallback method for BeautifulSoup parsing
        # LinkedIn primarily requires Selenium due to heavy JavaScript
        try:
            job_data = {}
            
            # Title
            title_element = job_element.find('a', {'class': 'base-card__full-link'})
            if title_element:
                job_data['title'] = title_element.get_text(strip=True)
                job_data['source_url'] = urljoin(self.base_url, title_element.get('href', ''))
            
            # Company
            company_element = job_element.find('h4', {'class': 'base-search-card__subtitle'})
            if company_element:
                job_data['company'] = company_element.get_text(strip=True)
            
            # Location
            location_element = job_element.find('span', {'class': 'job-search-card__location'})
            if location_element:
                job_data['location'] = location_element.get_text(strip=True)
            
            return job_data if job_data.get('title') else None
            
        except Exception as e:
            logger.error(f"Error parsing job listing: {e}")
            return None
    
    def _parse_posted_date(self, date_text: str) -> Optional[datetime]:
        """Parse posted date from LinkedIn format."""
        try:
            date_text = date_text.lower().strip()
            now = datetime.utcnow()
            
            if 'just now' in date_text or 'now' in date_text:
                return now
            elif 'minute' in date_text:
                minutes_match = re.search(r'(\d+)\s*minute', date_text)
                if minutes_match:
                    minutes = int(minutes_match.group(1))
                    return now - timedelta(minutes=minutes)
            elif 'hour' in date_text:
                hours_match = re.search(r'(\d+)\s*hour', date_text)
                if hours_match:
                    hours = int(hours_match.group(1))
                    return now - timedelta(hours=hours)
            elif 'day' in date_text:
                days_match = re.search(r'(\d+)\s*day', date_text)
                if days_match:
                    days = int(days_match.group(1))
                    return now - timedelta(days=days)
            elif 'week' in date_text:
                weeks_match = re.search(r'(\d+)\s*week', date_text)
                if weeks_match:
                    weeks = int(weeks_match.group(1))
                    return now - timedelta(weeks=weeks)
            elif 'month' in date_text:
                months_match = re.search(r'(\d+)\s*month', date_text)
                if months_match:
                    months = int(months_match.group(1))
                    return now - timedelta(days=months * 30)  # Approximate
            
            return None
            
        except Exception as e:
            logger.debug(f"Error parsing date '{date_text}': {e}")
            return None
