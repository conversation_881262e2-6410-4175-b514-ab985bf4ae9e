import time
import base64
from typing import Optional, Dict, Any
import logging
from twocaptcha import Two<PERSON><PERSON>tch<PERSON>

from config.settings import settings

logger = logging.getLogger(__name__)


class CaptchaSolver:
    """Handles CAPTCHA solving using 2captcha service."""
    
    def __init__(self):
        self.enabled = settings.captcha_enabled
        self.solver = None
        
        if self.enabled and settings.twocaptcha_api_key:
            try:
                self.solver = TwoCaptcha(settings.twocaptcha_api_key)
                logger.info("Initialized 2captcha solver")
            except Exception as e:
                logger.error(f"Failed to initialize 2captcha: {e}")
                self.enabled = False
        else:
            logger.info("CAPTCHA solving disabled or no API key provided")
    
    def solve_recaptcha_v2(self, site_key: str, page_url: str, timeout: int = 120) -> Optional[str]:
        """
        Solve reCAPTCHA v2.
        
        Args:
            site_key: The site key from the reCAPTCHA
            page_url: The URL of the page with the CAPTCHA
            timeout: Maximum time to wait for solution
            
        Returns:
            The CAPTCHA solution token or None if failed
        """
        if not self.enabled or not self.solver:
            logger.warning("CAPTCHA solving not available")
            return None
        
        try:
            logger.info(f"Solving reCAPTCHA v2 for {page_url}")
            result = self.solver.recaptcha(
                sitekey=site_key,
                url=page_url,
                timeout=timeout
            )
            
            if result and 'code' in result:
                logger.info("Successfully solved reCAPTCHA v2")
                return result['code']
            else:
                logger.error("Failed to solve reCAPTCHA v2")
                return None
                
        except Exception as e:
            logger.error(f"Error solving reCAPTCHA v2: {e}")
            return None
    
    def solve_recaptcha_v3(self, site_key: str, page_url: str, action: str = "submit", 
                          min_score: float = 0.3, timeout: int = 120) -> Optional[str]:
        """
        Solve reCAPTCHA v3.
        
        Args:
            site_key: The site key from the reCAPTCHA
            page_url: The URL of the page with the CAPTCHA
            action: The action parameter
            min_score: Minimum score required
            timeout: Maximum time to wait for solution
            
        Returns:
            The CAPTCHA solution token or None if failed
        """
        if not self.enabled or not self.solver:
            logger.warning("CAPTCHA solving not available")
            return None
        
        try:
            logger.info(f"Solving reCAPTCHA v3 for {page_url}")
            result = self.solver.recaptcha(
                sitekey=site_key,
                url=page_url,
                version='v3',
                action=action,
                score=min_score,
                timeout=timeout
            )
            
            if result and 'code' in result:
                logger.info("Successfully solved reCAPTCHA v3")
                return result['code']
            else:
                logger.error("Failed to solve reCAPTCHA v3")
                return None
                
        except Exception as e:
            logger.error(f"Error solving reCAPTCHA v3: {e}")
            return None
    
    def solve_hcaptcha(self, site_key: str, page_url: str, timeout: int = 120) -> Optional[str]:
        """
        Solve hCaptcha.
        
        Args:
            site_key: The site key from hCaptcha
            page_url: The URL of the page with the CAPTCHA
            timeout: Maximum time to wait for solution
            
        Returns:
            The CAPTCHA solution token or None if failed
        """
        if not self.enabled or not self.solver:
            logger.warning("CAPTCHA solving not available")
            return None
        
        try:
            logger.info(f"Solving hCaptcha for {page_url}")
            result = self.solver.hcaptcha(
                sitekey=site_key,
                url=page_url,
                timeout=timeout
            )
            
            if result and 'code' in result:
                logger.info("Successfully solved hCaptcha")
                return result['code']
            else:
                logger.error("Failed to solve hCaptcha")
                return None
                
        except Exception as e:
            logger.error(f"Error solving hCaptcha: {e}")
            return None
    
    def solve_image_captcha(self, image_path: str, timeout: int = 60) -> Optional[str]:
        """
        Solve image-based CAPTCHA.
        
        Args:
            image_path: Path to the CAPTCHA image file
            timeout: Maximum time to wait for solution
            
        Returns:
            The CAPTCHA solution text or None if failed
        """
        if not self.enabled or not self.solver:
            logger.warning("CAPTCHA solving not available")
            return None
        
        try:
            logger.info(f"Solving image CAPTCHA: {image_path}")
            result = self.solver.normal(image_path, timeout=timeout)
            
            if result and 'code' in result:
                logger.info("Successfully solved image CAPTCHA")
                return result['code']
            else:
                logger.error("Failed to solve image CAPTCHA")
                return None
                
        except Exception as e:
            logger.error(f"Error solving image CAPTCHA: {e}")
            return None
    
    def solve_image_captcha_base64(self, image_base64: str, timeout: int = 60) -> Optional[str]:
        """
        Solve image-based CAPTCHA from base64 string.
        
        Args:
            image_base64: Base64 encoded image data
            timeout: Maximum time to wait for solution
            
        Returns:
            The CAPTCHA solution text or None if failed
        """
        if not self.enabled or not self.solver:
            logger.warning("CAPTCHA solving not available")
            return None
        
        try:
            logger.info("Solving image CAPTCHA from base64")
            result = self.solver.normal(image_base64, timeout=timeout)
            
            if result and 'code' in result:
                logger.info("Successfully solved image CAPTCHA")
                return result['code']
            else:
                logger.error("Failed to solve image CAPTCHA")
                return None
                
        except Exception as e:
            logger.error(f"Error solving image CAPTCHA: {e}")
            return None
    
    def get_balance(self) -> Optional[float]:
        """Get account balance from 2captcha."""
        if not self.enabled or not self.solver:
            return None
        
        try:
            balance = self.solver.balance()
            logger.info(f"2captcha balance: ${balance}")
            return float(balance)
        except Exception as e:
            logger.error(f"Error getting 2captcha balance: {e}")
            return None


# Global captcha solver instance
captcha_solver = CaptchaSolver()


# Convenience functions for Selenium integration
def solve_recaptcha_selenium(driver, site_key: str, timeout: int = 120) -> bool:
    """
    Solve reCAPTCHA in Selenium and submit the solution.
    
    Args:
        driver: Selenium WebDriver instance
        site_key: The site key from the reCAPTCHA
        timeout: Maximum time to wait for solution
        
    Returns:
        True if successfully solved and submitted, False otherwise
    """
    try:
        current_url = driver.current_url
        solution = captcha_solver.solve_recaptcha_v2(site_key, current_url, timeout)
        
        if solution:
            # Inject the solution into the page
            script = f"""
            document.getElementById('g-recaptcha-response').innerHTML = '{solution}';
            if (typeof grecaptcha !== 'undefined') {{
                grecaptcha.getResponse = function() {{ return '{solution}'; }};
            }}
            """
            driver.execute_script(script)
            
            # Trigger the callback if it exists
            driver.execute_script("""
            if (window.recaptchaCallback) {
                window.recaptchaCallback();
            }
            """)
            
            return True
        
        return False
        
    except Exception as e:
        logger.error(f"Error solving reCAPTCHA with Selenium: {e}")
        return False
