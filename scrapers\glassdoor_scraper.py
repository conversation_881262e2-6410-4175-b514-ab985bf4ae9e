import time
import random
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from urllib.parse import urlencode, urljoin
import logging
import re

from bs4 import BeautifulSoup
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

from scrapers.base_scraper import BaseScraper

logger = logging.getLogger(__name__)


class GlassdoorScraper(BaseScraper):
    """Scraper for Glassdoor job postings. Uses Selenium due to heavy JavaScript."""
    
    def __init__(self):
        super().__init__("glassdoor")
        self.base_url = "https://www.glassdoor.com"
        self.search_url = "https://www.glassdoor.com/Job/jobs.htm"
        self.use_selenium = True  # Glassdoor requires JavaScript
    
    def scrape_jobs(self, search_terms: List[str], locations: List[str], max_pages: int = 10) -> int:
        """Scrape jobs from Glassdoor using Selenium."""
        total_jobs = 0
        
        # Initialize Selenium driver
        driver = self._get_selenium_driver()
        
        try:
            for search_term in search_terms:
                for location in locations:
                    logger.info(f"Scraping Glassdoor for '{search_term}' in '{location}'")
                    jobs_found = self._scrape_search_results_selenium(driver, search_term, location, max_pages)
                    total_jobs += jobs_found
                    
                    # Add delay between different search combinations
                    time.sleep(random.uniform(10, 15))
        
        finally:
            if driver:
                driver.quit()
        
        return total_jobs
    
    def _scrape_search_results_selenium(self, driver, search_term: str, location: str, max_pages: int) -> int:
        """Scrape search results using Selenium."""
        jobs_found = 0
        
        try:
            # Navigate to search page
            params = {
                'sc.keyword': search_term,
                'locT': 'C',
                'locId': location,
                'jobType': '',
                'fromAge': 1,  # Jobs from last day
                'minSalary': 0,
                'includeNoSalaryJobs': 'true',
                'radius': 25,
                'cityId': -1,
                'minRating': 0.0,
                'industryId': -1,
                'sgocId': -1,
                'seniorityType': '',
                'companyId': -1,
                'employerSizes': '',
                'applicationType': '',
                'remoteWorkType': 0
            }
            
            search_url = f"{self.search_url}?{urlencode(params)}"
            logger.debug(f"Navigating to: {search_url}")
            
            driver.get(search_url)
            time.sleep(random.uniform(3, 5))
            
            # Handle potential popups/modals
            self._handle_popups(driver)
            
            for page in range(max_pages):
                try:
                    logger.debug(f"Scraping page {page + 1}")
                    
                    # Wait for job listings to load
                    WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, '[data-test="job-listing"]'))
                    )
                    
                    # Get job listings
                    job_elements = driver.find_elements(By.CSS_SELECTOR, '[data-test="job-listing"]')
                    
                    if not job_elements:
                        logger.warning(f"No job listings found on page {page + 1}")
                        break
                    
                    # Parse each job listing
                    for job_element in job_elements:
                        try:
                            job_data = self.parse_job_listing_selenium(job_element)
                            if job_data:
                                if self._save_job(job_data):
                                    jobs_found += 1
                        except Exception as e:
                            logger.error(f"Error parsing job element: {e}")
                            continue
                    
                    self.stats['pages_scraped'] += 1
                    
                    # Try to go to next page
                    try:
                        next_button = driver.find_element(By.CSS_SELECTOR, '[data-test="pagination-next"]')
                        if next_button.is_enabled():
                            driver.execute_script("arguments[0].click();", next_button)
                            time.sleep(random.uniform(3, 6))
                        else:
                            logger.info(f"No more pages available after page {page + 1}")
                            break
                    except Exception:
                        logger.info(f"No next button found after page {page + 1}")
                        break
                
                except TimeoutException:
                    logger.warning(f"Timeout waiting for page {page + 1} to load")
                    break
                except Exception as e:
                    logger.error(f"Error scraping page {page + 1}: {e}")
                    self.stats['errors'] += 1
                    continue
        
        except Exception as e:
            logger.error(f"Error in Selenium scraping: {e}")
            self.stats['errors'] += 1
        
        return jobs_found
    
    def _handle_popups(self, driver):
        """Handle common popups and modals on Glassdoor."""
        try:
            # Close sign-up modal if present
            close_buttons = [
                '[data-test="modal-close"]',
                '.modal-close',
                '[aria-label="Close"]',
                '.close-button'
            ]
            
            for selector in close_buttons:
                try:
                    close_button = driver.find_element(By.CSS_SELECTOR, selector)
                    if close_button.is_displayed():
                        close_button.click()
                        time.sleep(1)
                        break
                except Exception:
                    continue
            
            # Handle cookie consent
            try:
                cookie_button = driver.find_element(By.CSS_SELECTOR, '[data-test="gdpr-consent-accept"]')
                if cookie_button.is_displayed():
                    cookie_button.click()
                    time.sleep(1)
            except Exception:
                pass
                
        except Exception as e:
            logger.debug(f"Error handling popups: {e}")
    
    def parse_job_listing_selenium(self, job_element) -> Optional[Dict[str, Any]]:
        """Parse a single Glassdoor job listing using Selenium."""
        try:
            job_data = {}
            
            # Title and URL
            try:
                title_element = job_element.find_element(By.CSS_SELECTOR, '[data-test="job-title"]')
                job_data['title'] = title_element.text.strip()
                
                # Get job URL
                title_link = title_element.find_element(By.TAG_NAME, 'a')
                href = title_link.get_attribute('href')
                job_data['source_url'] = href if href else self.base_url
            except Exception:
                return None
            
            # Company
            try:
                company_element = job_element.find_element(By.CSS_SELECTOR, '[data-test="employer-name"]')
                job_data['company'] = company_element.text.strip()
            except Exception:
                pass
            
            # Location
            try:
                location_element = job_element.find_element(By.CSS_SELECTOR, '[data-test="job-location"]')
                job_data['location'] = location_element.text.strip()
            except Exception:
                pass
            
            # Salary
            try:
                salary_element = job_element.find_element(By.CSS_SELECTOR, '[data-test="detailSalary"]')
                salary_text = salary_element.text.strip()
                salary_info = self._parse_salary(salary_text)
                job_data.update(salary_info)
            except Exception:
                pass
            
            # Job description/snippet
            try:
                description_element = job_element.find_element(By.CSS_SELECTOR, '[data-test="job-description"]')
                job_data['description'] = description_element.text.strip()
            except Exception:
                pass
            
            # Posted date
            try:
                date_element = job_element.find_element(By.CSS_SELECTOR, '[data-test="job-age"]')
                date_text = date_element.text.strip()
                posted_date = self._parse_posted_date(date_text)
                if posted_date:
                    job_data['posted_date'] = posted_date
            except Exception:
                pass
            
            # Check for remote work
            remote_indicators = ['remote', 'work from home', 'telecommute', 'virtual']
            job_text = f"{job_data.get('title', '')} {job_data.get('description', '')} {job_data.get('location', '')}".lower()
            job_data['is_remote'] = any(indicator in job_text for indicator in remote_indicators)
            
            # Set default values
            job_data.setdefault('job_type', 'full-time')
            job_data.setdefault('is_remote', False)
            job_data.setdefault('salary_currency', 'USD')
            
            return job_data
            
        except Exception as e:
            logger.error(f"Error parsing Selenium job listing: {e}")
            return None
    
    def parse_job_listing(self, job_element) -> Optional[Dict[str, Any]]:
        """Parse a single job listing (fallback for BeautifulSoup)."""
        # This is a fallback method for BeautifulSoup parsing
        # Glassdoor primarily requires Selenium due to heavy JavaScript
        try:
            job_data = {}
            
            # Title
            title_element = job_element.find('a', {'data-test': 'job-title'})
            if not title_element:
                title_element = job_element.find('h3')
            
            if title_element:
                job_data['title'] = title_element.get_text(strip=True)
                href = title_element.get('href')
                if href:
                    job_data['source_url'] = urljoin(self.base_url, href)
            
            # Company
            company_element = job_element.find('span', {'data-test': 'employer-name'})
            if company_element:
                job_data['company'] = company_element.get_text(strip=True)
            
            # Location
            location_element = job_element.find('span', {'data-test': 'job-location'})
            if location_element:
                job_data['location'] = location_element.get_text(strip=True)
            
            return job_data if job_data.get('title') else None
            
        except Exception as e:
            logger.error(f"Error parsing job listing: {e}")
            return None
    
    def _parse_salary(self, salary_text: str) -> Dict[str, Any]:
        """Parse salary information from Glassdoor format."""
        salary_info = {}
        
        try:
            # Remove currency symbols and normalize
            clean_text = re.sub(r'[,$]', '', salary_text.lower())
            
            # Glassdoor often shows ranges like "$50K - $70K (Glassdoor est.)"
            range_match = re.search(r'(\d+(?:\.\d+)?)\s*k?\s*[-–]\s*(\d+(?:\.\d+)?)\s*k?', clean_text)
            if range_match:
                min_sal, max_sal = range_match.groups()
                
                # Convert k notation
                if 'k' in clean_text:
                    salary_info['salary_min'] = float(min_sal) * 1000
                    salary_info['salary_max'] = float(max_sal) * 1000
                else:
                    salary_info['salary_min'] = float(min_sal)
                    salary_info['salary_max'] = float(max_sal)
            else:
                # Look for single salary value
                single_match = re.search(r'(\d+(?:\.\d+)?)\s*k?', clean_text)
                if single_match:
                    salary = float(single_match.group(1))
                    if 'k' in clean_text:
                        salary *= 1000
                    salary_info['salary_min'] = salary
                    salary_info['salary_max'] = salary
            
            # Glassdoor typically shows yearly salaries
            salary_info['salary_period'] = 'yearly'
            salary_info['salary_currency'] = 'USD'
            
        except Exception as e:
            logger.debug(f"Error parsing salary '{salary_text}': {e}")
        
        return salary_info
    
    def _parse_posted_date(self, date_text: str) -> Optional[datetime]:
        """Parse posted date from Glassdoor format."""
        try:
            date_text = date_text.lower().strip()
            now = datetime.utcnow()
            
            if 'today' in date_text or 'just posted' in date_text:
                return now
            elif 'yesterday' in date_text:
                return now - timedelta(days=1)
            else:
                # Look for "Xd" (X days) or "Xh" (X hours)
                days_match = re.search(r'(\d+)d', date_text)
                if days_match:
                    days = int(days_match.group(1))
                    return now - timedelta(days=days)
                
                hours_match = re.search(r'(\d+)h', date_text)
                if hours_match:
                    hours = int(hours_match.group(1))
                    return now - timedelta(hours=hours)
                
                # Standard format
                days_match = re.search(r'(\d+)\s*days?\s*ago', date_text)
                if days_match:
                    days = int(days_match.group(1))
                    return now - timedelta(days=days)
            
            return None
            
        except Exception as e:
            logger.debug(f"Error parsing date '{date_text}': {e}")
            return None
