from celery import Celery
from celery.schedules import crontab
import logging

from config.settings import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Celery app
app = Celery(
    'jobboard_scraper',
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=['scheduler.tasks']
)

# Celery configuration
app.conf.update(
    # Task settings
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    
    # Worker settings
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000,
    
    # Result backend settings
    result_expires=3600,  # 1 hour
    result_backend_transport_options={
        'master_name': 'mymaster',
        'visibility_timeout': 3600,
    },
    
    # Task routing
    task_routes={
        'scheduler.tasks.scrape_indeed': {'queue': 'scraping'},
        'scheduler.tasks.scrape_monster': {'queue': 'scraping'},
        'scheduler.tasks.scrape_glassdoor': {'queue': 'scraping'},
        'scheduler.tasks.scrape_linkedin': {'queue': 'scraping'},
        'scheduler.tasks.scrape_whatsjobs': {'queue': 'scraping'},
        'scheduler.tasks.scrape_dice': {'queue': 'scraping'},
        'scheduler.tasks.scrape_ziprecruiter': {'queue': 'scraping'},
        'scheduler.tasks.scrape_all_sources': {'queue': 'scraping'},
        'scheduler.tasks.cleanup_old_data': {'queue': 'maintenance'},
        'scheduler.tasks.health_check': {'queue': 'monitoring'},
    },
    
    # Beat schedule for periodic tasks
    beat_schedule={
        # Scrape all sources every 6 hours
        'scrape-all-sources': {
            'task': 'scheduler.tasks.scrape_all_sources',
            'schedule': crontab(minute=0, hour='*/6'),  # Every 6 hours
            'args': (['software engineer', 'data scientist', 'product manager'], ['New York', 'San Francisco', 'Remote'])
        },
        
        # Scrape Indeed more frequently (every 2 hours)
        'scrape-indeed-frequent': {
            'task': 'scheduler.tasks.scrape_indeed',
            'schedule': crontab(minute=0, hour='*/2'),  # Every 2 hours
            'args': (['python developer', 'javascript developer'], ['New York', 'Remote'])
        },
        
        # Daily cleanup of old data
        'cleanup-old-data': {
            'task': 'scheduler.tasks.cleanup_old_data',
            'schedule': crontab(hour=2, minute=0),  # Daily at 2 AM
            'args': (30,)  # Keep data for 30 days
        },
        
        # Health check every 15 minutes
        'health-check': {
            'task': 'scheduler.tasks.health_check',
            'schedule': crontab(minute='*/15'),  # Every 15 minutes
        },
        
        # Update proxy health every hour
        'update-proxy-health': {
            'task': 'scheduler.tasks.update_proxy_health',
            'schedule': crontab(minute=0),  # Every hour
        },
        
        # Generate daily reports
        'daily-report': {
            'task': 'scheduler.tasks.generate_daily_report',
            'schedule': crontab(hour=8, minute=0),  # Daily at 8 AM
        },
    },
)

# Error handling
@app.task(bind=True)
def debug_task(self):
    """Debug task for testing Celery setup."""
    print(f'Request: {self.request!r}')
    return 'Debug task completed'


# Task failure handler
@app.task(bind=True)
def handle_task_failure(self, task_id, error, traceback):
    """Handle task failures."""
    logger.error(f"Task {task_id} failed: {error}")
    logger.error(f"Traceback: {traceback}")
    
    # Here you could add notification logic (email, Slack, etc.)
    # For now, just log the error


# Configure error callbacks
app.conf.task_annotations = {
    '*': {
        'on_failure': handle_task_failure,
    }
}


if __name__ == '__main__':
    app.start()
