from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Query, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import logging
from datetime import datetime, timedelta

from config.settings import settings
from database.database import get_db, DatabaseManager
from database.models import JobPosting, ScrapingSession
from api.routes import jobs, admin
from scheduler.celery_app import app as celery_app

# Configure logging
logging.basicConfig(level=getattr(logging, settings.log_level))
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Job Board Scraper API",
    description="A comprehensive job board scraper service that aggregates job postings from multiple sources",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(jobs.router, prefix="/api/v1/jobs", tags=["jobs"])
app.include_router(admin.router, prefix="/api/v1/admin", tags=["admin"])


@app.on_event("startup")
async def startup_event():
    """Initialize the application on startup."""
    logger.info("Starting Job Board Scraper API")
    
    # Create database tables if they don't exist
    try:
        from database.database import create_tables
        create_tables()
        logger.info("Database tables initialized")
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
    
    # Check database health
    if DatabaseManager.health_check():
        logger.info("Database connection successful")
    else:
        logger.error("Database connection failed")


@app.on_event("shutdown")
async def shutdown_event():
    """Clean up on application shutdown."""
    logger.info("Shutting down Job Board Scraper API")


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Job Board Scraper API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Check database
        db_healthy = DatabaseManager.health_check()
        
        # Check Celery
        celery_healthy = True
        try:
            # Try to get Celery stats
            inspect = celery_app.control.inspect()
            stats = inspect.stats()
            if not stats:
                celery_healthy = False
        except Exception:
            celery_healthy = False
        
        # Get table counts
        table_counts = DatabaseManager.get_table_counts()
        
        health_status = {
            "status": "healthy" if db_healthy and celery_healthy else "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "components": {
                "database": "healthy" if db_healthy else "unhealthy",
                "celery": "healthy" if celery_healthy else "unhealthy"
            },
            "metrics": {
                "table_counts": table_counts
            }
        }
        
        status_code = 200 if health_status["status"] == "healthy" else 503
        return JSONResponse(content=health_status, status_code=status_code)
        
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return JSONResponse(
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            },
            status_code=503
        )


@app.get("/metrics")
async def get_metrics(db: Session = Depends(get_db)):
    """Get application metrics."""
    try:
        # Get job statistics
        total_jobs = db.query(JobPosting).count()
        active_jobs = db.query(JobPosting).filter(JobPosting.is_active == True).count()
        
        # Get jobs by source
        sources = db.query(JobPosting.source).distinct().all()
        jobs_by_source = {}
        for (source,) in sources:
            count = db.query(JobPosting).filter(JobPosting.source == source).count()
            jobs_by_source[source] = count
        
        # Get recent activity (last 24 hours)
        yesterday = datetime.utcnow() - timedelta(days=1)
        recent_jobs = db.query(JobPosting).filter(
            JobPosting.scraped_at >= yesterday
        ).count()
        
        recent_sessions = db.query(ScrapingSession).filter(
            ScrapingSession.started_at >= yesterday
        ).count()
        
        # Get job statistics by location (top 10)
        location_stats = db.query(
            JobPosting.location,
            db.func.count(JobPosting.id).label('count')
        ).filter(
            JobPosting.location.isnot(None),
            JobPosting.is_active == True
        ).group_by(JobPosting.location).order_by(
            db.func.count(JobPosting.id).desc()
        ).limit(10).all()
        
        metrics = {
            "timestamp": datetime.utcnow().isoformat(),
            "jobs": {
                "total": total_jobs,
                "active": active_jobs,
                "by_source": jobs_by_source,
                "recent_24h": recent_jobs
            },
            "scraping": {
                "recent_sessions_24h": recent_sessions
            },
            "top_locations": [
                {"location": loc, "count": count} 
                for loc, count in location_stats
            ]
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"Error getting metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/scrape/trigger")
async def trigger_scraping(
    background_tasks: BackgroundTasks,
    sources: Optional[List[str]] = Query(default=None, description="Sources to scrape (indeed, monster, glassdoor)"),
    search_terms: Optional[List[str]] = Query(default=["software engineer"], description="Job search terms"),
    locations: Optional[List[str]] = Query(default=["New York"], description="Locations to search"),
    max_pages: int = Query(default=5, description="Maximum pages to scrape per source")
):
    """Trigger manual scraping."""
    try:
        from scheduler.tasks import scrape_all_sources, scrape_indeed, scrape_monster, scrape_glassdoor
        
        if not sources:
            # Scrape all enabled sources
            task = scrape_all_sources.delay(search_terms, locations, max_pages)
        else:
            # Scrape specific sources
            tasks = []
            for source in sources:
                if source == "indeed":
                    task = scrape_indeed.delay(search_terms, locations, max_pages)
                elif source == "monster":
                    task = scrape_monster.delay(search_terms, locations, max_pages)
                elif source == "glassdoor":
                    task = scrape_glassdoor.delay(search_terms, locations, max_pages)
                else:
                    raise HTTPException(status_code=400, detail=f"Unknown source: {source}")
                tasks.append(task.id)
            
            if len(tasks) == 1:
                task_id = tasks[0]
            else:
                task_id = tasks
        
        return {
            "message": "Scraping triggered successfully",
            "task_id": task.id if not sources or len(sources) == 1 else tasks,
            "sources": sources or ["all"],
            "search_terms": search_terms,
            "locations": locations,
            "max_pages": max_pages
        }
        
    except Exception as e:
        logger.error(f"Error triggering scraping: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/tasks/{task_id}")
async def get_task_status(task_id: str):
    """Get the status of a Celery task."""
    try:
        from celery.result import AsyncResult
        
        result = AsyncResult(task_id, app=celery_app)
        
        response = {
            "task_id": task_id,
            "status": result.status,
            "result": result.result if result.ready() else None,
        }
        
        if result.status == 'PROGRESS':
            response["progress"] = result.info
        elif result.status == 'FAILURE':
            response["error"] = str(result.info)
        
        return response
        
    except Exception as e:
        logger.error(f"Error getting task status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=True,
        log_level=settings.log_level.lower()
    )
