import React, { useState } from 'react';
import { 
  Card, 
  Form, 
  InputNumber, 
  Switch, 
  Button, 
  Divider, 
  Alert, 
  Space,
  notification,
  Descriptions,
  Tag
} from 'antd';
import { SaveOutlined, ReloadOutlined, DeleteOutlined } from '@ant-design/icons';
import axios from 'axios';

const Settings = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [cleanupLoading, setCleanupLoading] = useState(false);

  const handleSave = async (values) => {
    setLoading(true);
    try {
      // In a real implementation, you would save these settings
      notification.success({
        message: 'Settings Saved',
        description: 'Configuration has been updated successfully.',
      });
    } catch (error) {
      notification.error({
        message: 'Error',
        description: 'Failed to save settings.',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCleanup = async (days) => {
    setCleanupLoading(true);
    try {
      const response = await axios.post(`/api/v1/admin/maintenance/cleanup?days=${days}`);
      notification.success({
        message: 'Cleanup Triggered',
        description: `Database cleanup task started. Task ID: ${response.data.task_id}`,
      });
    } catch (error) {
      notification.error({
        message: 'Error',
        description: 'Failed to trigger cleanup.',
      });
    } finally {
      setCleanupLoading(false);
    }
  };

  return (
    <div style={{ maxWidth: 800 }}>
      <Alert
        message="System Configuration"
        description="Modify scraping behavior and system settings. Changes may require restart to take effect."
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* Current Configuration */}
      <Card title="Current Configuration" style={{ marginBottom: 24 }}>
        <Descriptions column={2} bordered size="small">
          <Descriptions.Item label="Scraping Delay">2-5 seconds</Descriptions.Item>
          <Descriptions.Item label="Max Concurrent Requests">8</Descriptions.Item>
          <Descriptions.Item label="Download Timeout">30 seconds</Descriptions.Item>
          <Descriptions.Item label="Requests Per Minute">30</Descriptions.Item>
          <Descriptions.Item label="Proxy Enabled">
            <Tag color="red">Disabled</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="CAPTCHA Solving">
            <Tag color="red">Disabled</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Indeed Scraping">
            <Tag color="green">Enabled</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Monster Scraping">
            <Tag color="green">Enabled</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Glassdoor Scraping">
            <Tag color="green">Enabled</Tag>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* Scraping Settings */}
      <Card title="Scraping Configuration" style={{ marginBottom: 24 }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          initialValues={{
            scraping_delay_min: 2,
            scraping_delay_max: 5,
            max_concurrent_requests: 8,
            download_timeout: 30,
            requests_per_minute: 30,
            proxy_enabled: false,
            captcha_enabled: false,
            indeed_enabled: true,
            monster_enabled: true,
            glassdoor_enabled: true
          }}
        >
          <div className="settings-section">
            <h3>Rate Limiting</h3>
            <Form.Item
              name="scraping_delay_min"
              label="Minimum Delay Between Requests (seconds)"
              rules={[{ required: true, min: 1, max: 60 }]}
            >
              <InputNumber min={1} max={60} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="scraping_delay_max"
              label="Maximum Delay Between Requests (seconds)"
              rules={[{ required: true, min: 1, max: 60 }]}
            >
              <InputNumber min={1} max={60} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="max_concurrent_requests"
              label="Maximum Concurrent Requests"
              rules={[{ required: true, min: 1, max: 20 }]}
            >
              <InputNumber min={1} max={20} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="requests_per_minute"
              label="Requests Per Minute Limit"
              rules={[{ required: true, min: 1, max: 100 }]}
            >
              <InputNumber min={1} max={100} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="download_timeout"
              label="Download Timeout (seconds)"
              rules={[{ required: true, min: 5, max: 120 }]}
            >
              <InputNumber min={5} max={120} style={{ width: '100%' }} />
            </Form.Item>
          </div>

          <Divider />

          <div className="settings-section">
            <h3>Anti-Detection</h3>
            <Form.Item
              name="proxy_enabled"
              label="Enable Proxy Rotation"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name="captcha_enabled"
              label="Enable CAPTCHA Solving"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </div>

          <Divider />

          <div className="settings-section">
            <h3>Source Configuration</h3>
            <Form.Item
              name="indeed_enabled"
              label="Enable Indeed Scraping"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name="monster_enabled"
              label="Enable Monster Scraping"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name="glassdoor_enabled"
              label="Enable Glassdoor Scraping"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </div>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
              size="large"
            >
              Save Configuration
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* Maintenance */}
      <Card title="Database Maintenance" style={{ marginBottom: 24 }}>
        <Alert
          message="Database Cleanup"
          description="Remove old inactive job postings and scraping sessions to maintain performance."
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <p><strong>Cleanup Options:</strong></p>
            <Space wrap>
              <Button
                onClick={() => handleCleanup(7)}
                loading={cleanupLoading}
                icon={<DeleteOutlined />}
              >
                Clean 7+ days old
              </Button>
              <Button
                onClick={() => handleCleanup(30)}
                loading={cleanupLoading}
                icon={<DeleteOutlined />}
              >
                Clean 30+ days old
              </Button>
              <Button
                onClick={() => handleCleanup(90)}
                loading={cleanupLoading}
                icon={<DeleteOutlined />}
                danger
              >
                Clean 90+ days old
              </Button>
            </Space>
          </div>
        </Space>
      </Card>

      {/* System Information */}
      <Card title="System Information" size="small">
        <Descriptions column={1} size="small">
          <Descriptions.Item label="Application Version">1.0.0</Descriptions.Item>
          <Descriptions.Item label="Python Version">3.11+</Descriptions.Item>
          <Descriptions.Item label="Database">PostgreSQL</Descriptions.Item>
          <Descriptions.Item label="Task Queue">Celery + Redis</Descriptions.Item>
          <Descriptions.Item label="Web Framework">FastAPI</Descriptions.Item>
          <Descriptions.Item label="Scraping Framework">Scrapy + Selenium</Descriptions.Item>
        </Descriptions>
      </Card>
    </div>
  );
};

export default Settings;
