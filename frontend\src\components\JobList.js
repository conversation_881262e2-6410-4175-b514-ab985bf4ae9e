import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Input, 
  Select, 
  Button, 
  Space, 
  Tag, 
  Modal, 
  Descriptions,
  Pagination,
  Card,
  Row,
  Col,
  DatePicker,
  InputNumber,
  Switch
} from 'antd';
import { 
  SearchOutlined, 
  EyeOutlined, 
  ExternalLinkOutlined,
  FilterOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import axios from 'axios';
import moment from 'moment';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

const JobList = () => {
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [selectedJob, setSelectedJob] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    company: '',
    location: '',
    source: '',
    job_type: '',
    is_remote: null,
    min_salary: null,
    max_salary: null,
    posted_since: null,
    sort_by: 'posted_date',
    sort_order: 'desc'
  });

  useEffect(() => {
    fetchJobs();
  }, [currentPage, pageSize, filters]);

  const fetchJobs = async () => {
    setLoading(true);
    try {
      const params = {
        skip: (currentPage - 1) * pageSize,
        limit: pageSize,
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '' && value !== null)
        )
      };

      const response = await axios.get('/api/v1/jobs/', { params });
      setJobs(response.data.jobs);
      setTotal(response.data.total);
    } catch (error) {
      console.error('Error fetching jobs:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrent(1); // Reset to first page when filtering
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      company: '',
      location: '',
      source: '',
      job_type: '',
      is_remote: null,
      min_salary: null,
      max_salary: null,
      posted_since: null,
      sort_by: 'posted_date',
      sort_order: 'desc'
    });
    setCurrent(1);
  };

  const showJobDetails = (job) => {
    setSelectedJob(job);
    setModalVisible(true);
  };

  const formatSalary = (job) => {
    if (!job.salary_min && !job.salary_max) return 'Not specified';
    
    const formatAmount = (amount) => {
      if (amount >= 1000) {
        return `$${(amount / 1000).toFixed(0)}k`;
      }
      return `$${amount}`;
    };

    if (job.salary_min && job.salary_max) {
      return `${formatAmount(job.salary_min)} - ${formatAmount(job.salary_max)}`;
    } else if (job.salary_min) {
      return `${formatAmount(job.salary_min)}+`;
    } else {
      return `Up to ${formatAmount(job.salary_max)}`;
    }
  };

  const columns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      width: 250,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold', color: '#1890ff' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.company}</div>
        </div>
      ),
    },
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
      width: 150,
      render: (text) => text || 'Not specified',
    },
    {
      title: 'Source',
      dataIndex: 'source',
      key: 'source',
      width: 100,
      render: (source) => (
        <Tag color={
          source === 'indeed' ? 'blue' :
          source === 'monster' ? 'green' :
          source === 'glassdoor' ? 'orange' : 'default'
        }>
          {source.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'job_type',
      key: 'job_type',
      width: 100,
      render: (type) => type || 'Not specified',
    },
    {
      title: 'Remote',
      dataIndex: 'is_remote',
      key: 'is_remote',
      width: 80,
      render: (isRemote) => (
        <Tag color={isRemote ? 'green' : 'default'}>
          {isRemote ? 'Yes' : 'No'}
        </Tag>
      ),
    },
    {
      title: 'Salary',
      key: 'salary',
      width: 120,
      render: (_, record) => formatSalary(record),
    },
    {
      title: 'Posted',
      dataIndex: 'posted_date',
      key: 'posted_date',
      width: 100,
      render: (date) => date ? moment(date).fromNow() : 'Unknown',
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => showJobDetails(record)}
          >
            View
          </Button>
          <Button
            type="link"
            icon={<ExternalLinkOutlined />}
            onClick={() => window.open(record.source_url, '_blank')}
          >
            Open
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* Filters */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col xs={24} sm={12} md={6}>
            <Search
              placeholder="Search jobs..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              onSearch={() => fetchJobs()}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={12} md={4}>
            <Select
              placeholder="Source"
              value={filters.source}
              onChange={(value) => handleFilterChange('source', value)}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="indeed">Indeed</Option>
              <Option value="monster">Monster</Option>
              <Option value="glassdoor">Glassdoor</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={4}>
            <Select
              placeholder="Job Type"
              value={filters.job_type}
              onChange={(value) => handleFilterChange('job_type', value)}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="full-time">Full-time</Option>
              <Option value="part-time">Part-time</Option>
              <Option value="contract">Contract</Option>
              <Option value="internship">Internship</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={4}>
            <Select
              placeholder="Remote"
              value={filters.is_remote}
              onChange={(value) => handleFilterChange('is_remote', value)}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value={true}>Remote</Option>
              <Option value={false}>On-site</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Space>
              <Button 
                icon={<FilterOutlined />} 
                onClick={clearFilters}
              >
                Clear
              </Button>
              <Button 
                type="primary" 
                icon={<ReloadOutlined />} 
                onClick={fetchJobs}
              >
                Refresh
              </Button>
            </Space>
          </Col>
        </Row>
        
        <Row gutter={16} style={{ marginTop: 16 }}>
          <Col xs={24} sm={12} md={4}>
            <Input
              placeholder="Company"
              value={filters.company}
              onChange={(e) => handleFilterChange('company', e.target.value)}
            />
          </Col>
          <Col xs={24} sm={12} md={4}>
            <Input
              placeholder="Location"
              value={filters.location}
              onChange={(e) => handleFilterChange('location', e.target.value)}
            />
          </Col>
          <Col xs={24} sm={12} md={4}>
            <InputNumber
              placeholder="Min Salary"
              value={filters.min_salary}
              onChange={(value) => handleFilterChange('min_salary', value)}
              style={{ width: '100%' }}
              formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/\$\s?|(,*)/g, '')}
            />
          </Col>
          <Col xs={24} sm={12} md={4}>
            <InputNumber
              placeholder="Max Salary"
              value={filters.max_salary}
              onChange={(value) => handleFilterChange('max_salary', value)}
              style={{ width: '100%' }}
              formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/\$\s?|(,*)/g, '')}
            />
          </Col>
          <Col xs={24} sm={12} md={4}>
            <Select
              placeholder="Posted within"
              value={filters.posted_since}
              onChange={(value) => handleFilterChange('posted_since', value)}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value={1}>Last 24 hours</Option>
              <Option value={3}>Last 3 days</Option>
              <Option value={7}>Last week</Option>
              <Option value={30}>Last month</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={4}>
            <Select
              placeholder="Sort by"
              value={`${filters.sort_by}_${filters.sort_order}`}
              onChange={(value) => {
                const [sort_by, sort_order] = value.split('_');
                handleFilterChange('sort_by', sort_by);
                handleFilterChange('sort_order', sort_order);
              }}
              style={{ width: '100%' }}
            >
              <Option value="posted_date_desc">Newest first</Option>
              <Option value="posted_date_asc">Oldest first</Option>
              <Option value="salary_min_desc">Highest salary</Option>
              <Option value="salary_min_asc">Lowest salary</Option>
              <Option value="company_asc">Company A-Z</Option>
              <Option value="title_asc">Title A-Z</Option>
            </Select>
          </Col>
        </Row>
      </Card>

      {/* Jobs Table */}
      <Table
        columns={columns}
        dataSource={jobs}
        rowKey="id"
        loading={loading}
        pagination={false}
        scroll={{ x: 1200 }}
      />

      {/* Pagination */}
      <div style={{ textAlign: 'center', marginTop: 16 }}>
        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={total}
          showSizeChanger
          showQuickJumper
          showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} jobs`}
          onChange={(page, size) => {
            setCurrent(page);
            setPageSize(size);
          }}
        />
      </div>

      {/* Job Details Modal */}
      <Modal
        title="Job Details"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setModalVisible(false)}>
            Close
          </Button>,
          <Button 
            key="open" 
            type="primary" 
            icon={<ExternalLinkOutlined />}
            onClick={() => window.open(selectedJob?.source_url, '_blank')}
          >
            Open Original
          </Button>
        ]}
        width={800}
      >
        {selectedJob && (
          <Descriptions column={1} bordered>
            <Descriptions.Item label="Title">{selectedJob.title}</Descriptions.Item>
            <Descriptions.Item label="Company">{selectedJob.company}</Descriptions.Item>
            <Descriptions.Item label="Location">{selectedJob.location || 'Not specified'}</Descriptions.Item>
            <Descriptions.Item label="Job Type">{selectedJob.job_type || 'Not specified'}</Descriptions.Item>
            <Descriptions.Item label="Remote">
              <Tag color={selectedJob.is_remote ? 'green' : 'default'}>
                {selectedJob.is_remote ? 'Yes' : 'No'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Salary">{formatSalary(selectedJob)}</Descriptions.Item>
            <Descriptions.Item label="Source">
              <Tag color={
                selectedJob.source === 'indeed' ? 'blue' :
                selectedJob.source === 'monster' ? 'green' :
                selectedJob.source === 'glassdoor' ? 'orange' : 'default'
              }>
                {selectedJob.source.toUpperCase()}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Posted Date">
              {selectedJob.posted_date ? moment(selectedJob.posted_date).format('YYYY-MM-DD HH:mm') : 'Unknown'}
            </Descriptions.Item>
            <Descriptions.Item label="Scraped Date">
              {moment(selectedJob.scraped_at).format('YYYY-MM-DD HH:mm')}
            </Descriptions.Item>
            <Descriptions.Item label="Description">
              <div style={{ maxHeight: 200, overflowY: 'auto', whiteSpace: 'pre-wrap' }}>
                {selectedJob.description || 'No description available'}
              </div>
            </Descriptions.Item>
            {selectedJob.skills_required && (
              <Descriptions.Item label="Skills Required">
                <div style={{ maxHeight: 100, overflowY: 'auto' }}>
                  {selectedJob.skills_required}
                </div>
              </Descriptions.Item>
            )}
            {selectedJob.benefits && (
              <Descriptions.Item label="Benefits">
                <div style={{ maxHeight: 100, overflowY: 'auto' }}>
                  {selectedJob.benefits}
                </div>
              </Descriptions.Item>
            )}
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default JobList;
