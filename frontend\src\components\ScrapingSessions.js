import React, { useState, useEffect } from 'react';
import { Table, Tag, Card, Select, Button, Space, Statistic, Row, Col } from 'antd';
import { ReloadOutlined, CheckCircleOutlined, CloseCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import axios from 'axios';
import moment from 'moment';

const { Option } = Select;

const ScrapingSessions = () => {
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [filters, setFilters] = useState({
    source: '',
    status: '',
    days: 7
  });

  useEffect(() => {
    fetchSessions();
  }, [filters]);

  const fetchSessions = async () => {
    setLoading(true);
    try {
      const params = Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value !== '')
      );
      
      const response = await axios.get('/api/v1/admin/sessions', { params });
      setSessions(response.data.sessions);
      setTotal(response.data.total);
    } catch (error) {
      console.error('Error fetching sessions:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusTag = (status) => {
    const statusConfig = {
      completed: { color: 'green', icon: <CheckCircleOutlined /> },
      failed: { color: 'red', icon: <CloseCircleOutlined /> },
      running: { color: 'blue', icon: <ClockCircleOutlined /> }
    };

    const config = statusConfig[status] || { color: 'default', icon: null };
    
    return (
      <Tag color={config.color} icon={config.icon}>
        {status.toUpperCase()}
      </Tag>
    );
  };

  const calculateStats = () => {
    const completed = sessions.filter(s => s.status === 'completed').length;
    const failed = sessions.filter(s => s.status === 'failed').length;
    const running = sessions.filter(s => s.status === 'running').length;
    const totalJobs = sessions.reduce((sum, s) => sum + s.jobs_found, 0);
    const totalNew = sessions.reduce((sum, s) => sum + s.jobs_new, 0);

    return { completed, failed, running, totalJobs, totalNew };
  };

  const stats = calculateStats();

  const columns = [
    {
      title: 'Source',
      dataIndex: 'source',
      key: 'source',
      width: 100,
      render: (source) => (
        <Tag color={
          source === 'indeed' ? 'blue' :
          source === 'monster' ? 'green' :
          source === 'glassdoor' ? 'orange' : 'default'
        }>
          {source.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: getStatusTag,
    },
    {
      title: 'Started',
      dataIndex: 'started_at',
      key: 'started_at',
      width: 150,
      render: (date) => moment(date).format('MM/DD HH:mm'),
    },
    {
      title: 'Duration',
      key: 'duration',
      width: 100,
      render: (_, record) => {
        if (!record.completed_at) return 'Running...';
        const duration = moment(record.completed_at).diff(moment(record.started_at), 'minutes');
        return `${duration}m`;
      },
    },
    {
      title: 'Jobs Found',
      dataIndex: 'jobs_found',
      key: 'jobs_found',
      width: 100,
      align: 'center',
    },
    {
      title: 'New Jobs',
      dataIndex: 'jobs_new',
      key: 'jobs_new',
      width: 100,
      align: 'center',
      render: (value) => <span style={{ color: '#52c41a', fontWeight: 'bold' }}>{value}</span>,
    },
    {
      title: 'Updated',
      dataIndex: 'jobs_updated',
      key: 'jobs_updated',
      width: 100,
      align: 'center',
    },
    {
      title: 'Skipped',
      dataIndex: 'jobs_skipped',
      key: 'jobs_skipped',
      width: 100,
      align: 'center',
    },
    {
      title: 'Errors',
      dataIndex: 'errors_count',
      key: 'errors_count',
      width: 80,
      align: 'center',
      render: (value) => value > 0 ? 
        <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>{value}</span> : 
        value,
    },
    {
      title: 'Pages',
      dataIndex: 'pages_scraped',
      key: 'pages_scraped',
      width: 80,
      align: 'center',
    },
    {
      title: 'Requests',
      dataIndex: 'requests_made',
      key: 'requests_made',
      width: 100,
      align: 'center',
    },
    {
      title: 'Avg Response',
      dataIndex: 'avg_response_time',
      key: 'avg_response_time',
      width: 120,
      align: 'center',
      render: (time) => time ? `${time.toFixed(2)}s` : 'N/A',
    },
  ];

  const expandedRowRender = (record) => {
    return (
      <div style={{ padding: '16px', backgroundColor: '#fafafa' }}>
        <Row gutter={16}>
          <Col span={12}>
            <p><strong>Session ID:</strong> {record.id}</p>
            <p><strong>Started:</strong> {moment(record.started_at).format('YYYY-MM-DD HH:mm:ss')}</p>
            {record.completed_at && (
              <p><strong>Completed:</strong> {moment(record.completed_at).format('YYYY-MM-DD HH:mm:ss')}</p>
            )}
          </Col>
          <Col span={12}>
            <p><strong>Success Rate:</strong> {
              record.jobs_found > 0 ? 
                `${((record.jobs_new + record.jobs_updated) / record.jobs_found * 100).toFixed(1)}%` : 
                'N/A'
            }</p>
            <p><strong>Error Rate:</strong> {
              record.requests_made > 0 ? 
                `${(record.errors_count / record.requests_made * 100).toFixed(1)}%` : 
                'N/A'
            }</p>
          </Col>
        </Row>
        {record.error_details && (
          <div style={{ marginTop: 16 }}>
            <strong>Error Details:</strong>
            <div style={{ 
              backgroundColor: '#fff2f0', 
              border: '1px solid #ffccc7', 
              borderRadius: 4, 
              padding: 8, 
              marginTop: 8,
              maxHeight: 100,
              overflowY: 'auto'
            }}>
              {record.error_details}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div>
      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Completed"
              value={stats.completed}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Failed"
              value={stats.failed}
              prefix={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Jobs Found"
              value={stats.totalJobs}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="New Jobs Added"
              value={stats.totalNew}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: 16 }}>
        <Space>
          <Select
            placeholder="Filter by source"
            value={filters.source}
            onChange={(value) => setFilters(prev => ({ ...prev, source: value }))}
            style={{ width: 150 }}
            allowClear
          >
            <Option value="indeed">Indeed</Option>
            <Option value="monster">Monster</Option>
            <Option value="glassdoor">Glassdoor</Option>
          </Select>

          <Select
            placeholder="Filter by status"
            value={filters.status}
            onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
            style={{ width: 150 }}
            allowClear
          >
            <Option value="completed">Completed</Option>
            <Option value="failed">Failed</Option>
            <Option value="running">Running</Option>
          </Select>

          <Select
            placeholder="Time range"
            value={filters.days}
            onChange={(value) => setFilters(prev => ({ ...prev, days: value }))}
            style={{ width: 150 }}
          >
            <Option value={1}>Last 24 hours</Option>
            <Option value={3}>Last 3 days</Option>
            <Option value={7}>Last week</Option>
            <Option value={30}>Last month</Option>
          </Select>

          <Button 
            icon={<ReloadOutlined />} 
            onClick={fetchSessions}
            loading={loading}
          >
            Refresh
          </Button>
        </Space>
      </Card>

      {/* Sessions Table */}
      <Table
        columns={columns}
        dataSource={sessions}
        rowKey="id"
        loading={loading}
        expandable={{
          expandedRowRender,
          rowExpandable: (record) => true,
        }}
        scroll={{ x: 1200 }}
        pagination={{
          total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} sessions`,
        }}
      />
    </div>
  );
};

export default ScrapingSessions;
