# =============================================================================
# Job Board Scraper Configuration
# =============================================================================

# Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
LOG_FILE=logs/scraper.log

# =============================================================================
# Database Configuration
# =============================================================================
# MongoDB Configuration
MONGODB_URL=mongodb://localhost:27017/jobboard
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DATABASE=jobboard
MONGODB_USERNAME=
MONGODB_PASSWORD=
MONGODB_AUTH_SOURCE=admin

# Connection Pool Settings
MONGODB_MIN_POOL_SIZE=10
MONGODB_MAX_POOL_SIZE=100
MONGODB_MAX_IDLE_TIME_MS=30000

# =============================================================================
# Redis Configuration
# =============================================================================
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# =============================================================================
# Celery Configuration
# =============================================================================
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=json
CELERY_TIMEZONE=UTC

# =============================================================================
# API Configuration
# =============================================================================
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
API_SECRET_KEY=your-secret-key-here-change-in-production

# =============================================================================
# Scraping Configuration
# =============================================================================

# Rate Limiting
SCRAPING_DELAY_MIN=2
SCRAPING_DELAY_MAX=5
MAX_CONCURRENT_REQUESTS=8
DOWNLOAD_TIMEOUT=30
REQUESTS_PER_MINUTE=30

# User Agents
USER_AGENT_ROTATION=true
CUSTOM_USER_AGENTS=

# =============================================================================
# Anti-Detection Configuration
# =============================================================================

# Proxy Settings
PROXY_ENABLED=false
PROXY_LIST_URL=
PROXY_USERNAME=
PROXY_PASSWORD=
PROXY_ROTATION_INTERVAL=10

# CAPTCHA Solving
CAPTCHA_ENABLED=false
TWOCAPTCHA_API_KEY=
ANTICAPTCHA_API_KEY=

# =============================================================================
# Source Configuration
# =============================================================================

# Enable/Disable Sources
INDEED_ENABLED=true
MONSTER_ENABLED=true
GLASSDOOR_ENABLED=true
LINKEDIN_ENABLED=false
WHATSJOBS_ENABLED=true
DICE_ENABLED=true
ZIPRECRUITER_ENABLED=true

# Source-Specific Settings
LINKEDIN_EMAIL=
LINKEDIN_PASSWORD=
GLASSDOOR_EMAIL=
GLASSDOOR_PASSWORD=

# =============================================================================
# Selenium Configuration
# =============================================================================
SELENIUM_HEADLESS=true
SELENIUM_WINDOW_SIZE=1920,1080
SELENIUM_IMPLICIT_WAIT=10
SELENIUM_PAGE_LOAD_TIMEOUT=30
SELENIUM_SCRIPT_TIMEOUT=30

# Chrome Options
CHROME_NO_SANDBOX=true
CHROME_DISABLE_DEV_SHM_USAGE=true
CHROME_DISABLE_GPU=true

# =============================================================================
# Monitoring & Alerting
# =============================================================================

# Health Checks
HEALTH_CHECK_INTERVAL=300
HEALTH_CHECK_TIMEOUT=30

# Metrics
METRICS_ENABLED=true
PROMETHEUS_PORT=9090

# Alerting
SLACK_WEBHOOK_URL=
EMAIL_SMTP_HOST=
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=
EMAIL_PASSWORD=
EMAIL_FROM=
EMAIL_TO=

# =============================================================================
# Security Configuration
# =============================================================================

# Authentication
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# CORS
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000"]
CORS_ALLOW_CREDENTIALS=true

# =============================================================================
# Storage Configuration
# =============================================================================

# File Storage
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760  # 10MB

# S3 Configuration (Optional)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_BUCKET_NAME=
AWS_REGION=us-east-1

# =============================================================================
# Development Configuration
# =============================================================================

# Development Tools
RELOAD=true
WORKERS=1

# Testing
TEST_MONGODB_URL=mongodb://localhost:27017/jobboard_test
PYTEST_TIMEOUT=300

# =============================================================================
# Production Configuration
# =============================================================================

# SSL/TLS
SSL_ENABLED=false
SSL_CERT_PATH=
SSL_KEY_PATH=

# Load Balancing
NGINX_ENABLED=false
NGINX_CONFIG_PATH=nginx.conf

# Backup
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# =============================================================================
# Feature Flags
# =============================================================================

# Experimental Features
ENABLE_ADVANCED_FILTERING=true
ENABLE_ML_DEDUPLICATION=false
ENABLE_REAL_TIME_UPDATES=false
ENABLE_API_RATE_LIMITING=true

# =============================================================================
# External Services
# =============================================================================

# Analytics
GOOGLE_ANALYTICS_ID=
MIXPANEL_TOKEN=

# Error Tracking
SENTRY_DSN=
ROLLBAR_TOKEN=

# =============================================================================
# Custom Configuration
# =============================================================================

# Add your custom environment variables here
CUSTOM_SETTING_1=
CUSTOM_SETTING_2=
