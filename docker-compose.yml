version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: jobboard
      POSTGRES_USER: jobboard_user
      POSTGRES_PASSWORD: jobboard_pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U jobboard_user -d jobboard"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=******************************************************/jobboard
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    command: uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload

  celery_worker:
    build: .
    environment:
      - DATABASE_URL=******************************************************/jobboard
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    command: celery -A scheduler.celery_app worker --loglevel=info

  celery_beat:
    build: .
    environment:
      - DATABASE_URL=******************************************************/jobboard
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    command: celery -A scheduler.celery_app beat --loglevel=info

  flower:
    build: .
    ports:
      - "5555:5555"
    environment:
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - redis
    volumes:
      - .:/app
    command: celery -A scheduler.celery_app flower --port=5555

volumes:
  postgres_data:
  redis_data:
