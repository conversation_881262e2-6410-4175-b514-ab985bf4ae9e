version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: jobboard
      POSTGRES_USER: jobboard_user
      POSTGRES_PASSWORD: jobboard_pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U jobboard_user -d jobboard"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  api:
    build: .
    container_name: jobboard_api
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=******************************************************/jobboard
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
      - ./logs:/app/logs
      - /dev/shm:/dev/shm  # For Chrome in Docker
    command: uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  celery_worker:
    build: .
    container_name: jobboard_celery_worker
    environment:
      - DATABASE_URL=******************************************************/jobboard
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
      - ./logs:/app/logs
      - /dev/shm:/dev/shm  # For Chrome in Docker
    command: celery -A scheduler.celery_app worker --loglevel=info --concurrency=4
    healthcheck:
      test: ["CMD", "celery", "-A", "scheduler.celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  celery_beat:
    build: .
    container_name: jobboard_celery_beat
    environment:
      - DATABASE_URL=******************************************************/jobboard
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
      - ./logs:/app/logs
    command: celery -A scheduler.celery_app beat --loglevel=info

  flower:
    build: .
    container_name: jobboard_flower
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - redis
      - celery_worker
    volumes:
      - .:/app
    command: celery -A scheduler.celery_app flower --port=5555

  # Frontend (React Admin Dashboard)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: jobboard_frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - api

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    name: jobboard_network
