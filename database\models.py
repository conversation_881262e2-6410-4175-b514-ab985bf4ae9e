from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float, Index, UniqueConstraint
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.postgresql import UUID
import uuid

Base = declarative_base()


class JobPosting(Base):
    """Job posting model with comprehensive fields for job data."""
    
    __tablename__ = "job_postings"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Core job information
    title = Column(String(500), nullable=False, index=True)
    company = Column(String(200), nullable=False, index=True)
    location = Column(String(200), nullable=True, index=True)
    description = Column(Text, nullable=True)
    
    # Job details
    job_type = Column(String(50), nullable=True, index=True)  # full-time, part-time, contract, etc.
    experience_level = Column(String(50), nullable=True, index=True)  # entry, mid, senior, etc.
    salary_min = Column(Float, nullable=True)
    salary_max = Column(Float, nullable=True)
    salary_currency = Column(String(10), nullable=True, default='USD')
    salary_period = Column(String(20), nullable=True)  # hourly, monthly, yearly
    
    # Source information
    source = Column(String(50), nullable=False, index=True)  # indeed, monster, glassdoor
    source_url = Column(String(1000), nullable=False)
    source_job_id = Column(String(100), nullable=True)
    
    # Timestamps
    posted_date = Column(DateTime, nullable=True, index=True)
    scraped_at = Column(DateTime, nullable=False, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Status and metadata
    is_active = Column(Boolean, nullable=False, default=True, index=True)
    is_remote = Column(Boolean, nullable=False, default=False, index=True)
    skills_required = Column(Text, nullable=True)  # JSON string of skills
    benefits = Column(Text, nullable=True)
    
    # Deduplication and quality
    content_hash = Column(String(64), nullable=True, index=True)  # For deduplication
    quality_score = Column(Float, nullable=True, default=0.0)  # Quality scoring
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('source', 'source_url', name='uq_source_url'),
        Index('idx_location_title', 'location', 'title'),
        Index('idx_company_posted', 'company', 'posted_date'),
        Index('idx_salary_range', 'salary_min', 'salary_max'),
        Index('idx_active_recent', 'is_active', 'scraped_at'),
    )
    
    def __repr__(self):
        return f"<JobPosting(title='{self.title}', company='{self.company}', source='{self.source}')>"


class ScrapingSession(Base):
    """Track scraping sessions for monitoring and debugging."""
    
    __tablename__ = "scraping_sessions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    source = Column(String(50), nullable=False, index=True)
    started_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    status = Column(String(20), nullable=False, default='running', index=True)  # running, completed, failed
    
    # Statistics
    jobs_found = Column(Integer, nullable=False, default=0)
    jobs_new = Column(Integer, nullable=False, default=0)
    jobs_updated = Column(Integer, nullable=False, default=0)
    jobs_skipped = Column(Integer, nullable=False, default=0)
    
    # Error tracking
    errors_count = Column(Integer, nullable=False, default=0)
    error_details = Column(Text, nullable=True)
    
    # Performance metrics
    pages_scraped = Column(Integer, nullable=False, default=0)
    requests_made = Column(Integer, nullable=False, default=0)
    avg_response_time = Column(Float, nullable=True)
    
    def __repr__(self):
        return f"<ScrapingSession(source='{self.source}', status='{self.status}', jobs_found={self.jobs_found})>"


class ProxyStatus(Base):
    """Track proxy health and performance."""
    
    __tablename__ = "proxy_status"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    proxy_url = Column(String(200), nullable=False, unique=True)
    is_active = Column(Boolean, nullable=False, default=True, index=True)
    
    # Performance metrics
    success_rate = Column(Float, nullable=False, default=0.0)
    avg_response_time = Column(Float, nullable=True)
    total_requests = Column(Integer, nullable=False, default=0)
    failed_requests = Column(Integer, nullable=False, default=0)
    
    # Status tracking
    last_used = Column(DateTime, nullable=True)
    last_success = Column(DateTime, nullable=True)
    last_failure = Column(DateTime, nullable=True)
    failure_reason = Column(String(200), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<ProxyStatus(proxy_url='{self.proxy_url}', success_rate={self.success_rate})>"
