import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # MongoDB Database
    mongodb_url: str = Field(default="mongodb://localhost:27017/jobboard")
    mongodb_host: str = Field(default="localhost")
    mongodb_port: int = Field(default=27017)
    mongodb_database: str = Field(default="jobboard")
    mongodb_username: Optional[str] = Field(default=None)
    mongodb_password: Optional[str] = Field(default=None)
    mongodb_auth_source: str = Field(default="admin")
    mongodb_min_pool_size: int = Field(default=10)
    mongodb_max_pool_size: int = Field(default=100)
    mongodb_max_idle_time_ms: int = Field(default=30000)
    
    # Redis
    redis_url: str = Field(default="redis://localhost:6379/0")
    
    # API
    api_host: str = Field(default="0.0.0.0")
    api_port: int = Field(default=8000)
    api_workers: int = Field(default=4)
    secret_key: str = Field(default="your-secret-key-change-this-in-production")
    allowed_hosts: List[str] = Field(default=["localhost", "127.0.0.1"])
    
    # Scraping
    scraping_delay_min: int = Field(default=2)
    scraping_delay_max: int = Field(default=5)
    max_concurrent_requests: int = Field(default=8)
    download_timeout: int = Field(default=30)
    requests_per_minute: int = Field(default=30)
    burst_limit: int = Field(default=50)
    
    # Proxy Configuration
    proxy_enabled: bool = Field(default=False)
    proxy_list_url: Optional[str] = Field(default=None)
    proxy_username: Optional[str] = Field(default=None)
    proxy_password: Optional[str] = Field(default=None)
    
    # CAPTCHA
    captcha_enabled: bool = Field(default=False)
    twocaptcha_api_key: Optional[str] = Field(default=None)
    
    # Celery
    celery_broker_url: str = Field(default="redis://localhost:6379/0")
    celery_result_backend: str = Field(default="redis://localhost:6379/0")
    
    # Logging
    log_level: str = Field(default="INFO")
    log_file: str = Field(default="logs/scraper.log")
    
    # Scraping Targets
    indeed_enabled: bool = Field(default=True)
    monster_enabled: bool = Field(default=True)
    glassdoor_enabled: bool = Field(default=True)
    linkedin_enabled: bool = Field(default=True)
    whatsjobs_enabled: bool = Field(default=True)
    dice_enabled: bool = Field(default=True)
    ziprecruiter_enabled: bool = Field(default=True)
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()


# Scrapy settings
SCRAPY_SETTINGS = {
    'BOT_NAME': 'jobboard_scraper',
    'SPIDER_MODULES': ['scrapers.spiders'],
    'NEWSPIDER_MODULE': 'scrapers.spiders',
    'ROBOTSTXT_OBEY': True,
    'CONCURRENT_REQUESTS': settings.max_concurrent_requests,
    'DOWNLOAD_DELAY': settings.scraping_delay_min,
    'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
    'CONCURRENT_REQUESTS_PER_DOMAIN': 2,
    'AUTOTHROTTLE_ENABLED': True,
    'AUTOTHROTTLE_START_DELAY': 1,
    'AUTOTHROTTLE_MAX_DELAY': 10,
    'AUTOTHROTTLE_TARGET_CONCURRENCY': 2.0,
    'AUTOTHROTTLE_DEBUG': False,
    'HTTPCACHE_ENABLED': True,
    'HTTPCACHE_EXPIRATION_SECS': 3600,
    'USER_AGENT': 'jobboard_scraper (+http://www.yourdomain.com)',
    'DEFAULT_REQUEST_HEADERS': {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en',
    },
    'DOWNLOADER_MIDDLEWARES': {
        'utils.middlewares.ProxyMiddleware': 350,
        'utils.middlewares.UserAgentMiddleware': 400,
        'utils.middlewares.RetryMiddleware': 500,
    },
    'ITEM_PIPELINES': {
        'scrapers.pipelines.ValidationPipeline': 300,
        'scrapers.pipelines.DeduplicationPipeline': 400,
        'scrapers.pipelines.DatabasePipeline': 500,
    },
}


# User agents for rotation
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0',
]
