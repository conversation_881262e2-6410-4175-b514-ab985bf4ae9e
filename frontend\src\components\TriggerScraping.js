import React, { useState } from 'react';
import { 
  Form, 
  Input, 
  Select, 
  Button, 
  Card, 
  Alert, 
  Space, 
  Tag,
  InputNumber,
  Divider,
  notification
} from 'antd';
import { PlayCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import axios from 'axios';

const { Option } = Select;
const { TextArea } = Input;

const TriggerScraping = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [taskId, setTaskId] = useState(null);
  const [taskStatus, setTaskStatus] = useState(null);

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const response = await axios.post('/api/v1/admin/scraping/trigger', values);
      setTaskId(response.data.task_id);
      setTaskStatus('PENDING');
      
      notification.success({
        message: 'Scraping Triggered',
        description: `Task ID: ${response.data.task_id}`,
      });

      // Start polling for task status
      pollTaskStatus(response.data.task_id);
    } catch (error) {
      notification.error({
        message: 'Error',
        description: error.response?.data?.detail || 'Failed to trigger scraping',
      });
    } finally {
      setLoading(false);
    }
  };

  const pollTaskStatus = async (id) => {
    try {
      const response = await axios.get(`/api/v1/admin/tasks/${id}`);
      setTaskStatus(response.data.status);
      
      if (response.data.status === 'PENDING' || response.data.status === 'PROGRESS') {
        setTimeout(() => pollTaskStatus(id), 2000); // Poll every 2 seconds
      }
    } catch (error) {
      console.error('Error polling task status:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'SUCCESS': return 'green';
      case 'FAILURE': return 'red';
      case 'PROGRESS': return 'blue';
      case 'PENDING': return 'orange';
      default: return 'default';
    }
  };

  return (
    <div style={{ maxWidth: 800, margin: '0 auto' }}>
      <Card title="Trigger Manual Scraping" style={{ marginBottom: 24 }}>
        <Alert
          message="Manual Scraping"
          description="Use this form to trigger manual scraping jobs. Be mindful of rate limits and target site policies."
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            sources: ['indeed'],
            search_terms: ['software engineer', 'data scientist'],
            locations: ['New York', 'San Francisco', 'Remote'],
            max_pages: 5
          }}
        >
          <Form.Item
            name="sources"
            label="Sources to Scrape"
            rules={[{ required: true, message: 'Please select at least one source' }]}
          >
            <Select
              mode="multiple"
              placeholder="Select sources"
              style={{ width: '100%' }}
            >
              <Option value="indeed">Indeed</Option>
              <Option value="monster">Monster</Option>
              <Option value="glassdoor">Glassdoor</Option>
              <Option value="linkedin">LinkedIn</Option>
              <Option value="whatsjobs">WhatsJobs</Option>
              <Option value="dice">Dice</Option>
              <Option value="ziprecruiter">ZipRecruiter</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="search_terms"
            label="Search Terms"
            rules={[{ required: true, message: 'Please enter search terms' }]}
          >
            <Select
              mode="tags"
              placeholder="Enter job search terms"
              style={{ width: '100%' }}
            >
              <Option value="software engineer">Software Engineer</Option>
              <Option value="data scientist">Data Scientist</Option>
              <Option value="product manager">Product Manager</Option>
              <Option value="frontend developer">Frontend Developer</Option>
              <Option value="backend developer">Backend Developer</Option>
              <Option value="full stack developer">Full Stack Developer</Option>
              <Option value="devops engineer">DevOps Engineer</Option>
              <Option value="machine learning engineer">ML Engineer</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="locations"
            label="Locations"
            rules={[{ required: true, message: 'Please enter locations' }]}
          >
            <Select
              mode="tags"
              placeholder="Enter locations"
              style={{ width: '100%' }}
            >
              <Option value="New York">New York</Option>
              <Option value="San Francisco">San Francisco</Option>
              <Option value="Los Angeles">Los Angeles</Option>
              <Option value="Chicago">Chicago</Option>
              <Option value="Boston">Boston</Option>
              <Option value="Seattle">Seattle</Option>
              <Option value="Austin">Austin</Option>
              <Option value="Remote">Remote</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="max_pages"
            label="Maximum Pages per Source"
            rules={[{ required: true, message: 'Please enter max pages' }]}
          >
            <InputNumber
              min={1}
              max={20}
              style={{ width: '100%' }}
              placeholder="Number of pages to scrape"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<PlayCircleOutlined />}
              size="large"
              style={{ width: '100%' }}
            >
              Start Scraping
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {taskId && (
        <Card title="Task Status" style={{ marginBottom: 24 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <strong>Task ID:</strong> {taskId}
            </div>
            <div>
              <strong>Status:</strong> 
              <Tag color={getStatusColor(taskStatus)} style={{ marginLeft: 8 }}>
                {taskStatus}
              </Tag>
            </div>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => pollTaskStatus(taskId)}
              disabled={taskStatus === 'PENDING' || taskStatus === 'PROGRESS'}
            >
              Refresh Status
            </Button>
          </Space>
        </Card>
      )}

      <Card title="Quick Actions" size="small">
        <Space wrap>
          <Button
            onClick={() => {
              form.setFieldsValue({
                sources: ['indeed'],
                search_terms: ['software engineer'],
                locations: ['Remote'],
                max_pages: 1
              });
            }}
          >
            Test Indeed
          </Button>
          <Button
            onClick={() => {
              form.setFieldsValue({
                sources: ['monster'],
                search_terms: ['data scientist'],
                locations: ['New York'],
                max_pages: 1
              });
            }}
          >
            Test Monster
          </Button>
          <Button
            onClick={() => {
              form.setFieldsValue({
                sources: ['glassdoor'],
                search_terms: ['product manager'],
                locations: ['San Francisco'],
                max_pages: 1
              });
            }}
          >
            Test Glassdoor
          </Button>
          <Button
            onClick={() => {
              form.setFieldsValue({
                sources: ['indeed', 'monster', 'glassdoor', 'linkedin', 'whatsjobs', 'dice', 'ziprecruiter'],
                search_terms: ['software engineer', 'data scientist', 'product manager'],
                locations: ['New York', 'San Francisco', 'Remote'],
                max_pages: 3
              });
            }}
          >
            Full Scrape
          </Button>
          <Button
            onClick={() => {
              form.setFieldsValue({
                sources: ['linkedin'],
                search_terms: ['software engineer'],
                locations: ['Remote'],
                max_pages: 1
              });
            }}
          >
            Test LinkedIn
          </Button>
          <Button
            onClick={() => {
              form.setFieldsValue({
                sources: ['dice'],
                search_terms: ['python developer'],
                locations: ['New York'],
                max_pages: 1
              });
            }}
          >
            Test Dice
          </Button>
        </Space>
      </Card>
    </div>
  );
};

export default TriggerScraping;
