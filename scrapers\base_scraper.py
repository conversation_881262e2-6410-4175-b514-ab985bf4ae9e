import time
import random
import hashlib
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging
from urllib.parse import urljoin, urlparse

import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

from config.settings import settings
from database.mongodb import mongodb_manager
from utils.proxy_manager import proxy_manager
from utils.user_agent_manager import user_agent_manager
from utils.captcha_solver import captcha_solver

logger = logging.getLogger(__name__)


class BaseScraper(ABC):
    """Base class for all job site scrapers."""
    
    def __init__(self, source_name: str):
        self.source_name = source_name
        self.session = requests.Session()
        self.driver = None
        self.scraping_session_id = None
        self.stats = {
            'jobs_found': 0,
            'jobs_new': 0,
            'jobs_updated': 0,
            'jobs_skipped': 0,
            'errors': 0,
            'pages_scraped': 0,
            'requests_made': 0
        }
        
        # Configure session with headers and proxy
        self._configure_session()
    
    def _configure_session(self):
        """Configure the requests session with headers and proxy."""
        headers = user_agent_manager.get_headers_with_user_agent()
        self.session.headers.update(headers)
        
        # Set proxy if enabled
        proxy = proxy_manager.get_proxy()
        if proxy:
            self.session.proxies.update(proxy.proxy_dict)
            logger.debug(f"Using proxy: {proxy.url}")
    
    def _get_selenium_driver(self) -> webdriver.Chrome:
        """Create and configure a Selenium Chrome driver."""
        if self.driver:
            return self.driver
        
        options = ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # Set user agent
        user_agent = user_agent_manager.get_selenium_user_agent()
        options.add_argument(f'--user-agent={user_agent}')
        
        # Set proxy if enabled
        proxy = proxy_manager.get_proxy()
        if proxy:
            options.add_argument(f'--proxy-server={proxy.url}')
        
        try:
            self.driver = webdriver.Chrome(options=options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return self.driver
        except Exception as e:
            logger.error(f"Failed to create Selenium driver: {e}")
            raise
    
    def _make_request(self, url: str, method: str = 'GET', **kwargs) -> Optional[requests.Response]:
        """Make a request with error handling and retry logic."""
        max_retries = 3
        retry_delay = random.uniform(settings.scraping_delay_min, settings.scraping_delay_max)
        
        for attempt in range(max_retries):
            try:
                # Add random delay between requests
                if attempt > 0:
                    time.sleep(retry_delay * (attempt + 1))
                
                # Rotate user agent occasionally
                if random.random() < 0.1:  # 10% chance
                    headers = user_agent_manager.get_headers_with_user_agent()
                    self.session.headers.update(headers)
                
                response = self.session.request(method, url, timeout=settings.download_timeout, **kwargs)
                self.stats['requests_made'] += 1
                
                # Check for CAPTCHA or blocking
                if self._is_blocked(response):
                    logger.warning(f"Detected blocking on {url}")
                    if self._handle_blocking(response):
                        continue  # Retry after handling blocking
                    else:
                        return None
                
                response.raise_for_status()
                
                # Mark proxy as successful if used
                proxy = proxy_manager.get_proxy()
                if proxy:
                    proxy_manager.mark_proxy_success(proxy, response.elapsed.total_seconds())
                
                return response
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"Request failed (attempt {attempt + 1}): {e}")
                self.stats['errors'] += 1
                
                # Mark proxy as failed if used
                proxy = proxy_manager.get_proxy()
                if proxy:
                    proxy_manager.mark_proxy_failure(proxy, str(e))
                    # Rotate to next proxy
                    proxy_manager.rotate_proxy()
                    new_proxy = proxy_manager.get_proxy()
                    if new_proxy:
                        self.session.proxies.update(new_proxy.proxy_dict)
                
                if attempt == max_retries - 1:
                    logger.error(f"Failed to fetch {url} after {max_retries} attempts")
                    return None
        
        return None
    
    def _is_blocked(self, response: requests.Response) -> bool:
        """Check if the response indicates we're being blocked."""
        # Check status codes that indicate blocking
        if response.status_code in [403, 429, 503]:
            return True
        
        # Check for common blocking indicators in content
        content_lower = response.text.lower()
        blocking_indicators = [
            'captcha', 'blocked', 'access denied', 'rate limit',
            'too many requests', 'suspicious activity'
        ]
        
        return any(indicator in content_lower for indicator in blocking_indicators)
    
    def _handle_blocking(self, response: requests.Response) -> bool:
        """Handle blocking by solving CAPTCHAs or rotating proxies."""
        try:
            # Try to solve CAPTCHA if detected
            if 'captcha' in response.text.lower():
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Look for reCAPTCHA
                recaptcha_element = soup.find('div', {'class': 'g-recaptcha'})
                if recaptcha_element and recaptcha_element.get('data-sitekey'):
                    site_key = recaptcha_element['data-sitekey']
                    solution = captcha_solver.solve_recaptcha_v2(site_key, response.url)
                    if solution:
                        logger.info("Successfully solved CAPTCHA")
                        return True
            
            # Rotate proxy and user agent
            proxy_manager.rotate_proxy()
            new_proxy = proxy_manager.get_proxy()
            if new_proxy:
                self.session.proxies.update(new_proxy.proxy_dict)
            
            headers = user_agent_manager.get_headers_with_user_agent()
            self.session.headers.update(headers)
            
            # Add longer delay
            time.sleep(random.uniform(10, 20))
            
            return True
            
        except Exception as e:
            logger.error(f"Error handling blocking: {e}")
            return False
    
    async def _create_scraping_session(self) -> str:
        """Create a new scraping session record."""
        try:
            session_data = {
                'source': self.source_name,
                'started_at': datetime.utcnow(),
                'status': 'running',
                'jobs_found': 0,
                'jobs_new': 0,
                'jobs_updated': 0,
                'jobs_skipped': 0,
                'errors_count': 0,
                'pages_scraped': 0,
                'requests_made': 0
            }
            self.scraping_session_id = await mongodb_manager.save_scraping_session(session_data)
            return self.scraping_session_id
        except Exception as e:
            logger.error(f"Error creating scraping session: {e}")
            return None
    
    async def _update_scraping_session(self, status: str = 'completed'):
        """Update the scraping session with final statistics."""
        if not self.scraping_session_id:
            return

        try:
            update_data = {
                'status': status,
                'completed_at': datetime.utcnow(),
                'jobs_found': self.stats['jobs_found'],
                'jobs_new': self.stats['jobs_new'],
                'jobs_updated': self.stats['jobs_updated'],
                'jobs_skipped': self.stats['jobs_skipped'],
                'errors_count': self.stats['errors'],
                'pages_scraped': self.stats['pages_scraped'],
                'requests_made': self.stats['requests_made']
            }
            await mongodb_manager.update_scraping_session(self.scraping_session_id, update_data)

        except Exception as e:
            logger.error(f"Error updating scraping session: {e}")
    
    def _generate_content_hash(self, job_data: Dict[str, Any]) -> str:
        """Generate a hash for job content to detect duplicates."""
        content = f"{job_data.get('title', '')}{job_data.get('company', '')}{job_data.get('description', '')}"
        return hashlib.sha256(content.encode()).hexdigest()
    
    async def _save_job(self, job_data: Dict[str, Any]) -> bool:
        """Save a job posting to the database."""
        try:
            # Add metadata
            content_hash = self._generate_content_hash(job_data)
            job_data['content_hash'] = content_hash
            job_data['source'] = self.source_name

            # Save to MongoDB (it handles upsert logic)
            success = await mongodb_manager.save_job(job_data)

            if success:
                self.stats['jobs_found'] += 1
                self.stats['jobs_new'] += 1  # MongoDB handles the upsert logic
                logger.debug(f"Saved job: {job_data.get('title', 'Unknown')}")
                return True
            else:
                self.stats['errors'] += 1
                return False

        except Exception as e:
            logger.error(f"Error saving job: {e}")
            self.stats['errors'] += 1
            return False
    
    @abstractmethod
    def scrape_jobs(self, search_terms: List[str], locations: List[str], max_pages: int = 10) -> int:
        """
        Scrape jobs from the job site.
        
        Args:
            search_terms: List of job search terms
            locations: List of locations to search
            max_pages: Maximum number of pages to scrape
            
        Returns:
            Number of jobs scraped
        """
        pass
    
    @abstractmethod
    def parse_job_listing(self, job_element) -> Optional[Dict[str, Any]]:
        """
        Parse a single job listing element.
        
        Args:
            job_element: BeautifulSoup element or Selenium WebElement
            
        Returns:
            Dictionary with job data or None if parsing failed
        """
        pass
    
    def cleanup(self):
        """Clean up resources."""
        if self.driver:
            try:
                self.driver.quit()
            except Exception as e:
                logger.error(f"Error closing Selenium driver: {e}")
        
        if self.session:
            self.session.close()
    
    async def run_scraping(self, search_terms: List[str], locations: List[str], max_pages: int = 10) -> Dict[str, Any]:
        """
        Run the complete scraping process.

        Returns:
            Dictionary with scraping statistics
        """
        logger.info(f"Starting {self.source_name} scraping")
        await self._create_scraping_session()

        try:
            jobs_scraped = await self.scrape_jobs(search_terms, locations, max_pages)
            await self._update_scraping_session('completed')
            logger.info(f"Completed {self.source_name} scraping: {jobs_scraped} jobs processed")

        except Exception as e:
            logger.error(f"Error during {self.source_name} scraping: {e}")
            await self._update_scraping_session('failed')

        finally:
            self.cleanup()

        return self.stats
