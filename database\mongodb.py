import logging
from typing import Optional, Dict, Any, List
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase, AsyncIOMotorCollection
from pymongo import MongoClient, ASCENDING, DESCENDING
from pymongo.errors import Duplicate<PERSON><PERSON>Error, ConnectionFailure
import asyncio
from contextlib import asynccontextmanager

from config.settings import settings

logger = logging.getLogger(__name__)


class MongoDBManager:
    """MongoDB database manager for job board scraper."""
    
    def __init__(self):
        self.client: Optional[MongoClient] = None
        self.async_client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None
        self.collections = {}
        
    def connect(self):
        """Connect to MongoDB."""
        try:
            # Build connection URL
            if settings.mongodb_username and settings.mongodb_password:
                auth_string = f"{settings.mongodb_username}:{settings.mongodb_password}@"
            else:
                auth_string = ""
            
            connection_url = f"mongodb://{auth_string}{settings.mongodb_host}:{settings.mongodb_port}/{settings.mongodb_database}"
            
            # Sync client for admin operations
            self.client = MongoClient(
                connection_url,
                authSource=settings.mongodb_auth_source,
                minPoolSize=settings.mongodb_min_pool_size,
                maxPoolSize=settings.mongodb_max_pool_size,
                maxIdleTimeMS=settings.mongodb_max_idle_time_ms,
                serverSelectionTimeoutMS=5000
            )
            
            # Async client for application operations
            self.async_client = AsyncIOMotorClient(
                connection_url,
                authSource=settings.mongodb_auth_source,
                minPoolSize=settings.mongodb_min_pool_size,
                maxPoolSize=settings.mongodb_max_pool_size,
                maxIdleTimeMS=settings.mongodb_max_idle_time_ms,
                serverSelectionTimeoutMS=5000
            )
            
            self.database = self.async_client[settings.mongodb_database]
            
            # Test connection
            self.client.admin.command('ping')
            logger.info("Connected to MongoDB successfully")
            
            # Initialize collections
            self._initialize_collections()
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise
    
    def _initialize_collections(self):
        """Initialize MongoDB collections and indexes."""
        try:
            # Job postings collection
            self.collections['job_postings'] = self.database.job_postings
            
            # Scraping sessions collection
            self.collections['scraping_sessions'] = self.database.scraping_sessions
            
            # Proxy status collection
            self.collections['proxy_status'] = self.database.proxy_status
            
            # Create indexes
            self._create_indexes()
            
        except Exception as e:
            logger.error(f"Failed to initialize collections: {e}")
            raise
    
    def _create_indexes(self):
        """Create database indexes for better performance."""
        try:
            # Job postings indexes
            job_collection = self.client[settings.mongodb_database].job_postings
            
            # Compound index for search
            job_collection.create_index([
                ("title", "text"),
                ("description", "text"),
                ("company", "text")
            ], name="search_index")
            
            # Individual field indexes
            job_collection.create_index("source", name="source_index")
            job_collection.create_index("location", name="location_index")
            job_collection.create_index("company", name="company_index")
            job_collection.create_index("posted_date", name="posted_date_index")
            job_collection.create_index("scraped_at", name="scraped_at_index")
            job_collection.create_index("is_active", name="is_active_index")
            job_collection.create_index("is_remote", name="is_remote_index")
            
            # Unique index for deduplication
            job_collection.create_index([
                ("source", ASCENDING),
                ("source_job_id", ASCENDING)
            ], unique=True, name="unique_job_index", sparse=True)
            
            # Scraping sessions indexes
            session_collection = self.client[settings.mongodb_database].scraping_sessions
            session_collection.create_index("source", name="session_source_index")
            session_collection.create_index("started_at", name="session_started_index")
            session_collection.create_index("status", name="session_status_index")
            
            # Proxy status indexes
            proxy_collection = self.client[settings.mongodb_database].proxy_status
            proxy_collection.create_index("proxy_url", unique=True, name="proxy_url_index")
            proxy_collection.create_index("is_active", name="proxy_active_index")
            
            logger.info("Database indexes created successfully")
            
        except Exception as e:
            logger.error(f"Failed to create indexes: {e}")
    
    def disconnect(self):
        """Disconnect from MongoDB."""
        try:
            if self.client:
                self.client.close()
            if self.async_client:
                self.async_client.close()
            logger.info("Disconnected from MongoDB")
        except Exception as e:
            logger.error(f"Error disconnecting from MongoDB: {e}")
    
    def health_check(self) -> bool:
        """Check MongoDB connection health."""
        try:
            if not self.client:
                return False
            
            # Ping the database
            self.client.admin.command('ping')
            return True
            
        except Exception as e:
            logger.error(f"MongoDB health check failed: {e}")
            return False
    
    async def save_job(self, job_data: Dict[str, Any]) -> bool:
        """Save a job posting to MongoDB."""
        try:
            # Add metadata
            job_data['scraped_at'] = datetime.utcnow()
            job_data['updated_at'] = datetime.utcnow()
            job_data['is_active'] = True
            
            # Try to insert or update
            filter_query = {
                "source": job_data.get("source"),
                "source_url": job_data.get("source_url")
            }
            
            # If source_job_id exists, use it for deduplication
            if job_data.get("source_job_id"):
                filter_query["source_job_id"] = job_data["source_job_id"]
            
            result = await self.collections['job_postings'].update_one(
                filter_query,
                {"$set": job_data},
                upsert=True
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error saving job: {e}")
            return False
    
    async def save_scraping_session(self, session_data: Dict[str, Any]) -> str:
        """Save a scraping session to MongoDB."""
        try:
            session_data['created_at'] = datetime.utcnow()
            session_data['updated_at'] = datetime.utcnow()
            
            result = await self.collections['scraping_sessions'].insert_one(session_data)
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"Error saving scraping session: {e}")
            return None
    
    async def update_scraping_session(self, session_id: str, update_data: Dict[str, Any]) -> bool:
        """Update a scraping session."""
        try:
            from bson import ObjectId
            
            update_data['updated_at'] = datetime.utcnow()
            
            result = await self.collections['scraping_sessions'].update_one(
                {"_id": ObjectId(session_id)},
                {"$set": update_data}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating scraping session: {e}")
            return False
    
    async def get_jobs(self, 
                      skip: int = 0, 
                      limit: int = 50, 
                      filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Get job postings with filtering and pagination."""
        try:
            query = {"is_active": True}
            
            if filters:
                # Text search
                if filters.get('search'):
                    query["$text"] = {"$search": filters['search']}
                
                # Field filters
                for field in ['source', 'company', 'location', 'job_type', 'is_remote']:
                    if filters.get(field) is not None:
                        if field in ['company', 'location'] and isinstance(filters[field], str):
                            query[field] = {"$regex": filters[field], "$options": "i"}
                        else:
                            query[field] = filters[field]
                
                # Salary range
                if filters.get('min_salary'):
                    query["salary_min"] = {"$gte": filters['min_salary']}
                if filters.get('max_salary'):
                    query["salary_max"] = {"$lte": filters['max_salary']}
                
                # Date range
                if filters.get('posted_since'):
                    from datetime import timedelta
                    since_date = datetime.utcnow() - timedelta(days=filters['posted_since'])
                    query["posted_date"] = {"$gte": since_date}
            
            # Sorting
            sort_field = filters.get('sort_by', 'posted_date') if filters else 'posted_date'
            sort_order = DESCENDING if filters and filters.get('sort_order') == 'desc' else ASCENDING
            
            cursor = self.collections['job_postings'].find(query).sort(sort_field, sort_order).skip(skip).limit(limit)
            jobs = await cursor.to_list(length=limit)
            
            # Convert ObjectId to string
            for job in jobs:
                job['id'] = str(job['_id'])
                del job['_id']
            
            return jobs
            
        except Exception as e:
            logger.error(f"Error getting jobs: {e}")
            return []
    
    async def get_job_count(self, filters: Dict[str, Any] = None) -> int:
        """Get total count of jobs matching filters."""
        try:
            query = {"is_active": True}
            
            if filters:
                # Apply same filters as get_jobs
                if filters.get('search'):
                    query["$text"] = {"$search": filters['search']}
                
                for field in ['source', 'company', 'location', 'job_type', 'is_remote']:
                    if filters.get(field) is not None:
                        if field in ['company', 'location'] and isinstance(filters[field], str):
                            query[field] = {"$regex": filters[field], "$options": "i"}
                        else:
                            query[field] = filters[field]
                
                if filters.get('min_salary'):
                    query["salary_min"] = {"$gte": filters['min_salary']}
                if filters.get('max_salary'):
                    query["salary_max"] = {"$lte": filters['max_salary']}
                
                if filters.get('posted_since'):
                    from datetime import timedelta
                    since_date = datetime.utcnow() - timedelta(days=filters['posted_since'])
                    query["posted_date"] = {"$gte": since_date}
            
            return await self.collections['job_postings'].count_documents(query)
            
        except Exception as e:
            logger.error(f"Error getting job count: {e}")
            return 0
    
    async def cleanup_old_data(self, days: int = 30):
        """Clean up old inactive job postings and sessions."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            # Remove old inactive jobs
            job_result = await self.collections['job_postings'].delete_many({
                "is_active": False,
                "updated_at": {"$lt": cutoff_date}
            })
            
            # Remove old scraping sessions
            session_result = await self.collections['scraping_sessions'].delete_many({
                "started_at": {"$lt": cutoff_date}
            })
            
            logger.info(f"Cleanup completed: {job_result.deleted_count} jobs, {session_result.deleted_count} sessions removed")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def get_table_counts(self) -> Dict[str, int]:
        """Get document counts for all collections."""
        try:
            db = self.client[settings.mongodb_database]
            return {
                'job_postings': db.job_postings.count_documents({}),
                'scraping_sessions': db.scraping_sessions.count_documents({}),
                'proxy_status': db.proxy_status.count_documents({})
            }
        except Exception as e:
            logger.error(f"Error getting table counts: {e}")
            return {}


# Global MongoDB manager instance
mongodb_manager = MongoDBManager()


# Dependency for FastAPI
async def get_mongodb():
    """Dependency to get MongoDB database instance."""
    return mongodb_manager.database


@asynccontextmanager
async def get_mongodb_session():
    """Context manager for MongoDB operations."""
    try:
        yield mongodb_manager
    except Exception as e:
        logger.error(f"MongoDB session error: {e}")
        raise


def create_collections():
    """Create MongoDB collections and indexes."""
    try:
        mongodb_manager.connect()
        logger.info("MongoDB collections and indexes created successfully")
    except Exception as e:
        logger.error(f"Failed to create MongoDB collections: {e}")
        raise


def init_mongodb():
    """Initialize MongoDB connection."""
    mongodb_manager.connect()


def close_mongodb():
    """Close MongoDB connection."""
    mongodb_manager.disconnect()
