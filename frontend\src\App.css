.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Custom styles for the dashboard */
.dashboard-card {
  margin-bottom: 16px;
}

.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-number {
  font-size: 2em;
  font-weight: bold;
  color: #1890ff;
}

.stat-label {
  color: #666;
  margin-top: 8px;
}

.chart-container {
  height: 300px;
  margin-top: 20px;
}

.job-card {
  margin-bottom: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
}

.job-title {
  font-size: 18px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8px;
}

.job-company {
  font-size: 16px;
  color: #666;
  margin-bottom: 4px;
}

.job-location {
  color: #999;
  margin-bottom: 8px;
}

.job-meta {
  display: flex;
  gap: 16px;
  margin-top: 12px;
}

.job-meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 14px;
}

.session-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.session-status.completed {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.session-status.failed {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.session-status.running {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.error-log {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
}

.error-log-header {
  font-weight: bold;
  color: #ff4d4f;
  margin-bottom: 8px;
}

.error-log-details {
  color: #666;
  font-size: 14px;
}

.trigger-form {
  max-width: 600px;
  margin: 0 auto;
}

.trigger-form .ant-form-item {
  margin-bottom: 24px;
}

.settings-section {
  margin-bottom: 32px;
}

.settings-section h3 {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
  margin-bottom: 16px;
}
