import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Layout, Menu, theme } from 'antd';
import {
  DashboardOutlined,
  DatabaseOutlined,
  SettingOutlined,
  BugOutlined,
  PlayCircleOutlined,
  BarChartOutlined
} from '@ant-design/icons';

import Dashboard from './components/Dashboard';
import JobList from './components/JobList';
import ScrapingSessions from './components/ScrapingSessions';
import Settings from './components/Settings';
import ErrorLogs from './components/ErrorLogs';
import TriggerScraping from './components/TriggerScraping';

import './App.css';

const { Header, Content, Sider } = Layout;

const menuItems = [
  {
    key: '/',
    icon: <DashboardOutlined />,
    label: 'Dashboard',
  },
  {
    key: '/jobs',
    icon: <DatabaseOutlined />,
    label: 'Job Listings',
  },
  {
    key: '/sessions',
    icon: <BarChartOutlined />,
    label: 'Scraping Sessions',
  },
  {
    key: '/trigger',
    icon: <PlayCircleOutlined />,
    label: 'Trigger Scraping',
  },
  {
    key: '/errors',
    icon: <BugOutlined />,
    label: 'Error Logs',
  },
  {
    key: '/settings',
    icon: <SettingOutlined />,
    label: 'Settings',
  },
];

function App() {
  const {
    token: { colorBgContainer },
  } = theme.useToken();

  const [selectedKey, setSelectedKey] = React.useState('/');

  return (
    <Router>
      <Layout style={{ minHeight: '100vh' }}>
        <Sider
          breakpoint="lg"
          collapsedWidth="0"
          onBreakpoint={(broken) => {
            console.log(broken);
          }}
          onCollapse={(collapsed, type) => {
            console.log(collapsed, type);
          }}
        >
          <div className="demo-logo-vertical" style={{ 
            height: 32, 
            margin: 16, 
            background: 'rgba(255, 255, 255, 0.3)',
            borderRadius: 6,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontWeight: 'bold'
          }}>
            Job Scraper
          </div>
          <Menu
            theme="dark"
            mode="inline"
            selectedKeys={[selectedKey]}
            items={menuItems}
            onClick={({ key }) => {
              setSelectedKey(key);
              window.history.pushState(null, '', key);
            }}
          />
        </Sider>
        <Layout>
          <Header
            style={{
              padding: 0,
              background: colorBgContainer,
              display: 'flex',
              alignItems: 'center',
              paddingLeft: 24,
            }}
          >
            <h1 style={{ margin: 0, fontSize: '20px', fontWeight: 'bold' }}>
              Job Board Scraper - Admin Dashboard
            </h1>
          </Header>
          <Content
            style={{
              margin: '24px 16px',
              padding: 24,
              minHeight: 280,
              background: colorBgContainer,
            }}
          >
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/jobs" element={<JobList />} />
              <Route path="/sessions" element={<ScrapingSessions />} />
              <Route path="/trigger" element={<TriggerScraping />} />
              <Route path="/errors" element={<ErrorLogs />} />
              <Route path="/settings" element={<Settings />} />
            </Routes>
          </Content>
        </Layout>
      </Layout>
    </Router>
  );
}

export default App;
