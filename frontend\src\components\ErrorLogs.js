import React, { useState, useEffect } from 'react';
import { Card, List, Tag, Button, Select, Alert, Empty, Spin } from 'antd';
import { ReloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import axios from 'axios';
import moment from 'moment';

const { Option } = Select;

const ErrorLogs = () => {
  const [errors, setErrors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    hours: 24,
    limit: 50
  });

  useEffect(() => {
    fetchErrors();
  }, [filters]);

  const fetchErrors = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/v1/admin/logs/errors', {
        params: filters
      });
      setErrors(response.data.errors);
    } catch (error) {
      console.error('Error fetching error logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSourceColor = (source) => {
    const colors = {
      indeed: 'blue',
      monster: 'green',
      glassdoor: 'orange'
    };
    return colors[source] || 'default';
  };

  const getStatusColor = (status) => {
    const colors = {
      failed: 'red',
      completed: 'orange', // Completed but with errors
      running: 'blue'
    };
    return colors[status] || 'default';
  };

  if (loading && errors.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p>Loading error logs...</p>
      </div>
    );
  }

  return (
    <div>
      <Alert
        message="Error Monitoring"
        description="This page shows recent scraping errors and failures. Monitor this regularly to identify issues with scrapers."
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* Filters */}
      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', gap: 16, alignItems: 'center' }}>
          <span>Show errors from:</span>
          <Select
            value={filters.hours}
            onChange={(value) => setFilters(prev => ({ ...prev, hours: value }))}
            style={{ width: 150 }}
          >
            <Option value={1}>Last hour</Option>
            <Option value={6}>Last 6 hours</Option>
            <Option value={24}>Last 24 hours</Option>
            <Option value={72}>Last 3 days</Option>
            <Option value={168}>Last week</Option>
          </Select>

          <span>Limit:</span>
          <Select
            value={filters.limit}
            onChange={(value) => setFilters(prev => ({ ...prev, limit: value }))}
            style={{ width: 100 }}
          >
            <Option value={25}>25</Option>
            <Option value={50}>50</Option>
            <Option value={100}>100</Option>
            <Option value={200}>200</Option>
          </Select>

          <Button 
            icon={<ReloadOutlined />} 
            onClick={fetchErrors}
            loading={loading}
          >
            Refresh
          </Button>
        </div>
      </Card>

      {/* Error Summary */}
      {errors.length > 0 && (
        <Card 
          title={`${errors.length} Error Sessions Found`} 
          style={{ marginBottom: 16 }}
          size="small"
        >
          <div style={{ display: 'flex', gap: 24 }}>
            <div>
              <strong>Sources:</strong>
              {[...new Set(errors.map(e => e.source))].map(source => (
                <Tag key={source} color={getSourceColor(source)} style={{ marginLeft: 8 }}>
                  {source.toUpperCase()}
                </Tag>
              ))}
            </div>
            <div>
              <strong>Total Error Count:</strong> {errors.reduce((sum, e) => sum + e.error_count, 0)}
            </div>
          </div>
        </Card>
      )}

      {/* Error List */}
      {errors.length === 0 ? (
        <Card>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="No errors found in the selected time range"
          />
        </Card>
      ) : (
        <List
          dataSource={errors}
          renderItem={(error) => (
            <Card 
              style={{ marginBottom: 16 }}
              size="small"
              title={
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <Tag color={getSourceColor(error.source)}>
                      {error.source.toUpperCase()}
                    </Tag>
                    <Tag color={getStatusColor(error.status)}>
                      {error.status.toUpperCase()}
                    </Tag>
                    <span style={{ marginLeft: 8 }}>
                      {error.error_count} error{error.error_count !== 1 ? 's' : ''}
                    </span>
                  </div>
                  <div style={{ fontSize: '14px', fontWeight: 'normal', color: '#666' }}>
                    {moment(error.started_at).format('MM/DD/YYYY HH:mm')} 
                    ({moment(error.started_at).fromNow()})
                  </div>
                </div>
              }
            >
              <div style={{ marginBottom: 12 }}>
                <strong>Session ID:</strong> {error.session_id}
              </div>
              
              {error.error_details && (
                <div>
                  <strong>Error Details:</strong>
                  <div style={{
                    backgroundColor: '#fff2f0',
                    border: '1px solid #ffccc7',
                    borderRadius: 4,
                    padding: 12,
                    marginTop: 8,
                    fontFamily: 'monospace',
                    fontSize: '12px',
                    maxHeight: 200,
                    overflowY: 'auto',
                    whiteSpace: 'pre-wrap'
                  }}>
                    {error.error_details}
                  </div>
                </div>
              )}
            </Card>
          )}
        />
      )}
    </div>
  );
};

export default ErrorLogs;
