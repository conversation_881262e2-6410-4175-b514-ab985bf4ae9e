# Job Board Scraper Service

A comprehensive, scalable job board scraper that aggregates job postings from multiple sources including Indeed, Monster, Glassdoor, LinkedIn, WhatsJobs, Dice, and ZipRecruiter.

## 🚀 Features

### Core Functionality
- **Multi-Source Scraping**: Supports 7+ major job boards
- **Intelligent Anti-Detection**: Proxy rotation, user-agent rotation, CAPTCHA solving
- **Distributed Architecture**: Celery + Redis for scalable task processing
- **Real-time Monitoring**: Admin dashboard with live statistics
- **Data Deduplication**: Prevents duplicate job postings
- **RESTful API**: Full-featured API for job search and management

### Supported Job Boards
- ✅ **Indeed** - Static scraping with BeautifulSoup
- ✅ **Monster** - Static scraping with BeautifulSoup  
- ✅ **Glassdoor** - Dynamic scraping with Selenium
- ✅ **LinkedIn** - Dynamic scraping with Selenium (use with caution)
- ✅ **WhatsJobs** - Static scraping with BeautifulSoup
- ✅ **Dice** - Static scraping with BeautifulSoup (tech-focused)
- ✅ **ZipRecruiter** - Static scraping with BeautifulSoup

### Anti-Detection Features
- **Proxy Rotation**: Automatic proxy switching with health monitoring
- **User-Agent Rotation**: Dynamic user-agent strings
- **Request Throttling**: Configurable delays between requests
- **CAPTCHA Solving**: Integration with 2captcha service
- **Rate Limiting**: Respects robots.txt and implements smart delays

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   FastAPI       │    │   Scrapers      │
│   (React)       │◄──►│   Backend       │◄──►│   (Scrapy +     │
│                 │    │                 │    │    Selenium)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   PostgreSQL    │              │
         └──────────────►│   Database      │◄─────────────┘
                        └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │   Celery +      │
                        │   Redis         │
                        │   (Task Queue)  │
                        └─────────────────┘
```

## 🛠️ Installation

### Prerequisites
- Python 3.11+
- PostgreSQL 12+
- Redis 6+
- Node.js 16+ (for frontend)
- Chrome/Chromium (for Selenium)

### Quick Start with Docker

1. **Clone the repository**
```bash
git clone <repository-url>
cd job-board-scraper
```

2. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start services**
```bash
docker-compose up -d
```

4. **Initialize database**
```bash
docker-compose exec api python -c "from database.database import create_tables; create_tables()"
```

5. **Access the application**
- API: http://localhost:8000
- Admin Dashboard: http://localhost:3000
- API Documentation: http://localhost:8000/docs
- Flower (Celery Monitor): http://localhost:5555

### Manual Installation

1. **Install Python dependencies**
```bash
pip install -r requirements.txt
```

2. **Install Playwright browsers**
```bash
playwright install chromium
```

3. **Setup PostgreSQL database**
```bash
createdb jobboard
```

4. **Setup Redis**
```bash
# Install and start Redis server
redis-server
```

5. **Initialize database**
```bash
python -c "from database.database import create_tables; create_tables()"
```

6. **Start services**
```bash
# Terminal 1: API Server
uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload

# Terminal 2: Celery Worker
celery -A scheduler.celery_app worker --loglevel=info

# Terminal 3: Celery Beat (Scheduler)
celery -A scheduler.celery_app beat --loglevel=info

# Terminal 4: Frontend (optional)
cd frontend
npm install
npm start
```

## 📖 Usage

### Command Line Interface

**Test a single scraper:**
```bash
python run_scraper.py --source indeed --test
```

**Run full scraping:**
```bash
python run_scraper.py --source all --search-terms "software engineer" "data scientist" --locations "New York" "Remote" --max-pages 5
```

**Initialize database:**
```bash
python run_scraper.py --init-db
```

### API Usage

**Get jobs:**
```bash
curl "http://localhost:8000/api/v1/jobs/?search=python&location=remote&limit=10"
```

**Trigger scraping:**
```bash
curl -X POST "http://localhost:8000/api/v1/scrape/trigger" \
  -H "Content-Type: application/json" \
  -d '{"sources": ["indeed"], "search_terms": ["python developer"], "locations": ["New York"]}'
```

**Get health status:**
```bash
curl "http://localhost:8000/health"
```

### Admin Dashboard

Access the React-based admin dashboard at http://localhost:3000 to:
- Monitor scraping sessions
- View job statistics
- Trigger manual scraping
- Manage system settings
- View error logs

## ⚙️ Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Database
DATABASE_URL=postgresql://jobboard_user:jobboard_pass@localhost:5432/jobboard

# Redis
REDIS_URL=redis://localhost:6379/0

# Scraping Settings
SCRAPING_DELAY_MIN=2
SCRAPING_DELAY_MAX=5
MAX_CONCURRENT_REQUESTS=8

# Anti-Detection
PROXY_ENABLED=false
CAPTCHA_ENABLED=false
TWOCAPTCHA_API_KEY=your_api_key

# Source Control
INDEED_ENABLED=true
MONSTER_ENABLED=true
GLASSDOOR_ENABLED=true
LINKEDIN_ENABLED=true
WHATSJOBS_ENABLED=true
DICE_ENABLED=true
ZIPRECRUITER_ENABLED=true
```

### Proxy Configuration

To enable proxy rotation:

1. Set `PROXY_ENABLED=true`
2. Add proxy URLs to the database or configure `PROXY_LIST_URL`
3. Set proxy credentials if required

### CAPTCHA Solving

To enable CAPTCHA solving:

1. Sign up for 2captcha service
2. Set `CAPTCHA_ENABLED=true`
3. Set `TWOCAPTCHA_API_KEY=your_api_key`

## 🔒 Legal & Ethical Considerations

### Important Notes
- **Respect robots.txt**: The scraper checks and respects robots.txt by default
- **Rate limiting**: Implements delays and throttling to avoid overwhelming servers
- **Terms of Service**: Review each site's ToS before scraping
- **Data usage**: Only scrape publicly available data
- **LinkedIn**: Use with extreme caution due to strict anti-bot measures

### Best Practices
- Use reasonable delays between requests (2-5 seconds)
- Limit concurrent requests per domain
- Monitor for blocking and adjust accordingly
- Consider using official APIs when available
- Implement proper error handling and logging

## 📊 Monitoring

### Health Checks
- Database connectivity
- Celery worker status
- Redis connectivity
- Recent scraping activity

### Metrics
- Jobs scraped per source
- Success/failure rates
- Response times
- Error frequencies

### Logging
- Structured logging with configurable levels
- Error tracking and alerting
- Performance metrics
- Audit trails

## 🧪 Testing

**Run tests:**
```bash
pytest tests/
```

**Test individual scrapers:**
```bash
python run_scraper.py --source indeed --test --verbose
```

**Load testing:**
```bash
# Test API endpoints
curl -X POST "http://localhost:8000/api/v1/admin/scraping/trigger" \
  -H "Content-Type: application/json" \
  -d '{"sources": ["indeed"], "search_terms": ["test"], "locations": ["test"], "max_pages": 1}'
```

## 🚀 Deployment

### Production Considerations

1. **Security**
   - Change default passwords
   - Use environment variables for secrets
   - Enable HTTPS
   - Implement authentication

2. **Scaling**
   - Use multiple Celery workers
   - Implement load balancing
   - Use Redis Cluster for high availability
   - Consider database read replicas

3. **Monitoring**
   - Set up application monitoring (e.g., Prometheus + Grafana)
   - Configure log aggregation (e.g., ELK stack)
   - Implement alerting for failures

### Docker Production

```bash
# Production docker-compose
docker-compose -f docker-compose.prod.yml up -d
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ⚠️ Disclaimer

This tool is for educational and research purposes. Users are responsible for ensuring compliance with applicable laws, terms of service, and ethical guidelines when scraping websites. The authors are not responsible for any misuse of this software.
