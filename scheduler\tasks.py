from celery import current_task
from datetime import datetime, timedelta
import logging
from typing import List, Dict, Any

from scheduler.celery_app import app
from scrapers.indeed_scraper import IndeedScraper
from scrapers.monster_scraper import MonsterScraper
from scrapers.glassdoor_scraper import GlassdoorScraper
from scrapers.linkedin_scraper import LinkedInScraper
from scrapers.whatsjobs_scraper import WhatsJobsScraper
from scrapers.dice_scraper import DiceScraper
from scrapers.ziprecruiter_scraper import ZipR<PERSON>ruiterScraper
from database.database import DatabaseManager, get_db_session
from database.models import JobPosting, ScrapingSession, ProxyStatus
from utils.proxy_manager import proxy_manager
from config.settings import settings

logger = logging.getLogger(__name__)


@app.task(bind=True)
def scrape_indeed(self, search_terms: List[str], locations: List[str], max_pages: int = 10):
    """Scrape jobs from Indeed."""
    if not settings.indeed_enabled:
        logger.info("Indeed scraping is disabled")
        return {"status": "disabled", "source": "indeed"}
    
    try:
        logger.info(f"Starting Indeed scraping task: {self.request.id}")
        scraper = IndeedScraper()
        
        # Update task state
        self.update_state(state='PROGRESS', meta={'status': 'Starting Indeed scraper'})
        
        stats = scraper.run_scraping(search_terms, locations, max_pages)
        
        logger.info(f"Indeed scraping completed: {stats}")
        return {
            "status": "completed",
            "source": "indeed",
            "stats": stats,
            "task_id": self.request.id
        }
        
    except Exception as e:
        logger.error(f"Error in Indeed scraping task: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@app.task(bind=True)
def scrape_monster(self, search_terms: List[str], locations: List[str], max_pages: int = 10):
    """Scrape jobs from Monster."""
    if not settings.monster_enabled:
        logger.info("Monster scraping is disabled")
        return {"status": "disabled", "source": "monster"}
    
    try:
        logger.info(f"Starting Monster scraping task: {self.request.id}")
        scraper = MonsterScraper()
        
        self.update_state(state='PROGRESS', meta={'status': 'Starting Monster scraper'})
        
        stats = scraper.run_scraping(search_terms, locations, max_pages)
        
        logger.info(f"Monster scraping completed: {stats}")
        return {
            "status": "completed",
            "source": "monster",
            "stats": stats,
            "task_id": self.request.id
        }
        
    except Exception as e:
        logger.error(f"Error in Monster scraping task: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@app.task(bind=True)
def scrape_glassdoor(self, search_terms: List[str], locations: List[str], max_pages: int = 5):
    """Scrape jobs from Glassdoor."""
    if not settings.glassdoor_enabled:
        logger.info("Glassdoor scraping is disabled")
        return {"status": "disabled", "source": "glassdoor"}
    
    try:
        logger.info(f"Starting Glassdoor scraping task: {self.request.id}")
        scraper = GlassdoorScraper()
        
        self.update_state(state='PROGRESS', meta={'status': 'Starting Glassdoor scraper'})
        
        # Use fewer pages for Glassdoor as it's slower
        stats = scraper.run_scraping(search_terms, locations, max_pages)
        
        logger.info(f"Glassdoor scraping completed: {stats}")
        return {
            "status": "completed",
            "source": "glassdoor",
            "stats": stats,
            "task_id": self.request.id
        }
        
    except Exception as e:
        logger.error(f"Error in Glassdoor scraping task: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@app.task(bind=True)
def scrape_linkedin(self, search_terms: List[str], locations: List[str], max_pages: int = 5):
    """Scrape jobs from LinkedIn."""
    if not settings.linkedin_enabled:
        logger.info("LinkedIn scraping is disabled")
        return {"status": "disabled", "source": "linkedin"}

    try:
        logger.info(f"Starting LinkedIn scraping task: {self.request.id}")
        scraper = LinkedInScraper()

        self.update_state(state='PROGRESS', meta={'status': 'Starting LinkedIn scraper'})

        # Use fewer pages for LinkedIn as it's slower and more restrictive
        stats = scraper.run_scraping(search_terms, locations, max_pages)

        logger.info(f"LinkedIn scraping completed: {stats}")
        return {
            "status": "completed",
            "source": "linkedin",
            "stats": stats,
            "task_id": self.request.id
        }

    except Exception as e:
        logger.error(f"Error in LinkedIn scraping task: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@app.task(bind=True)
def scrape_whatsjobs(self, search_terms: List[str], locations: List[str], max_pages: int = 10):
    """Scrape jobs from WhatsJobs."""
    if not settings.whatsjobs_enabled:
        logger.info("WhatsJobs scraping is disabled")
        return {"status": "disabled", "source": "whatsjobs"}

    try:
        logger.info(f"Starting WhatsJobs scraping task: {self.request.id}")
        scraper = WhatsJobsScraper()

        self.update_state(state='PROGRESS', meta={'status': 'Starting WhatsJobs scraper'})

        stats = scraper.run_scraping(search_terms, locations, max_pages)

        logger.info(f"WhatsJobs scraping completed: {stats}")
        return {
            "status": "completed",
            "source": "whatsjobs",
            "stats": stats,
            "task_id": self.request.id
        }

    except Exception as e:
        logger.error(f"Error in WhatsJobs scraping task: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@app.task(bind=True)
def scrape_dice(self, search_terms: List[str], locations: List[str], max_pages: int = 10):
    """Scrape jobs from Dice."""
    if not settings.dice_enabled:
        logger.info("Dice scraping is disabled")
        return {"status": "disabled", "source": "dice"}

    try:
        logger.info(f"Starting Dice scraping task: {self.request.id}")
        scraper = DiceScraper()

        self.update_state(state='PROGRESS', meta={'status': 'Starting Dice scraper'})

        stats = scraper.run_scraping(search_terms, locations, max_pages)

        logger.info(f"Dice scraping completed: {stats}")
        return {
            "status": "completed",
            "source": "dice",
            "stats": stats,
            "task_id": self.request.id
        }

    except Exception as e:
        logger.error(f"Error in Dice scraping task: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@app.task(bind=True)
def scrape_ziprecruiter(self, search_terms: List[str], locations: List[str], max_pages: int = 10):
    """Scrape jobs from ZipRecruiter."""
    if not settings.ziprecruiter_enabled:
        logger.info("ZipRecruiter scraping is disabled")
        return {"status": "disabled", "source": "ziprecruiter"}

    try:
        logger.info(f"Starting ZipRecruiter scraping task: {self.request.id}")
        scraper = ZipRecruiterScraper()

        self.update_state(state='PROGRESS', meta={'status': 'Starting ZipRecruiter scraper'})

        stats = scraper.run_scraping(search_terms, locations, max_pages)

        logger.info(f"ZipRecruiter scraping completed: {stats}")
        return {
            "status": "completed",
            "source": "ziprecruiter",
            "stats": stats,
            "task_id": self.request.id
        }

    except Exception as e:
        logger.error(f"Error in ZipRecruiter scraping task: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@app.task(bind=True)
def scrape_all_sources(self, search_terms: List[str], locations: List[str], max_pages: int = 10):
    """Scrape jobs from all enabled sources."""
    try:
        logger.info(f"Starting scraping from all sources: {self.request.id}")
        
        results = {}
        total_stats = {
            'jobs_found': 0,
            'jobs_new': 0,
            'jobs_updated': 0,
            'jobs_skipped': 0,
            'errors': 0
        }
        
        # Scrape from each enabled source
        sources = []
        if settings.indeed_enabled:
            sources.append(('indeed', IndeedScraper))
        if settings.monster_enabled:
            sources.append(('monster', MonsterScraper))
        if settings.glassdoor_enabled:
            sources.append(('glassdoor', GlassdoorScraper))
        if settings.linkedin_enabled:
            sources.append(('linkedin', LinkedInScraper))
        if settings.whatsjobs_enabled:
            sources.append(('whatsjobs', WhatsJobsScraper))
        if settings.dice_enabled:
            sources.append(('dice', DiceScraper))
        if settings.ziprecruiter_enabled:
            sources.append(('ziprecruiter', ZipRecruiterScraper))
        
        for i, (source_name, scraper_class) in enumerate(sources):
            try:
                self.update_state(
                    state='PROGRESS', 
                    meta={
                        'status': f'Scraping {source_name}',
                        'current': i + 1,
                        'total': len(sources)
                    }
                )
                
                scraper = scraper_class()
                stats = scraper.run_scraping(search_terms, locations, max_pages)
                results[source_name] = stats
                
                # Aggregate stats
                for key in total_stats:
                    total_stats[key] += stats.get(key, 0)
                
                logger.info(f"Completed {source_name}: {stats}")
                
            except Exception as e:
                logger.error(f"Error scraping {source_name}: {e}")
                results[source_name] = {"error": str(e)}
        
        logger.info(f"All sources scraping completed. Total stats: {total_stats}")
        
        return {
            "status": "completed",
            "sources": results,
            "total_stats": total_stats,
            "task_id": self.request.id
        }
        
    except Exception as e:
        logger.error(f"Error in scrape_all_sources task: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@app.task
def cleanup_old_data(days: int = 30):
    """Clean up old job postings and scraping sessions."""
    try:
        logger.info(f"Starting cleanup of data older than {days} days")
        
        DatabaseManager.cleanup_old_data(days)
        
        logger.info("Data cleanup completed successfully")
        return {"status": "completed", "days": days}
        
    except Exception as e:
        logger.error(f"Error in cleanup task: {e}")
        raise


@app.task
def health_check():
    """Perform health check on the system."""
    try:
        health_status = {
            "timestamp": datetime.utcnow().isoformat(),
            "database": DatabaseManager.health_check(),
            "table_counts": DatabaseManager.get_table_counts(),
        }
        
        # Check recent scraping activity
        try:
            with get_db_session() as session:
                recent_sessions = session.query(ScrapingSession).filter(
                    ScrapingSession.started_at > datetime.utcnow() - timedelta(hours=24)
                ).count()
                health_status["recent_scraping_sessions"] = recent_sessions
        except Exception as e:
            health_status["recent_scraping_sessions"] = f"Error: {e}"
        
        logger.info(f"Health check completed: {health_status}")
        return health_status
        
    except Exception as e:
        logger.error(f"Error in health check: {e}")
        raise


@app.task
def update_proxy_health():
    """Update proxy health status."""
    try:
        logger.info("Starting proxy health update")
        
        if not settings.proxy_enabled:
            return {"status": "disabled", "message": "Proxy rotation is disabled"}
        
        # Test all proxies
        healthy_proxies = proxy_manager.get_healthy_proxies()
        
        result = {
            "status": "completed",
            "total_proxies": len(proxy_manager.proxies),
            "healthy_proxies": len(healthy_proxies),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info(f"Proxy health update completed: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Error updating proxy health: {e}")
        raise


@app.task
def generate_daily_report():
    """Generate daily scraping report."""
    try:
        logger.info("Generating daily report")
        
        yesterday = datetime.utcnow() - timedelta(days=1)
        
        with get_db_session() as session:
            # Get yesterday's scraping sessions
            sessions = session.query(ScrapingSession).filter(
                ScrapingSession.started_at >= yesterday,
                ScrapingSession.started_at < datetime.utcnow()
            ).all()
            
            # Get new jobs from yesterday
            new_jobs = session.query(JobPosting).filter(
                JobPosting.scraped_at >= yesterday,
                JobPosting.scraped_at < datetime.utcnow()
            ).count()
            
            # Aggregate stats by source
            source_stats = {}
            for session_record in sessions:
                source = session_record.source
                if source not in source_stats:
                    source_stats[source] = {
                        'sessions': 0,
                        'jobs_found': 0,
                        'jobs_new': 0,
                        'errors': 0
                    }
                
                source_stats[source]['sessions'] += 1
                source_stats[source]['jobs_found'] += session_record.jobs_found
                source_stats[source]['jobs_new'] += session_record.jobs_new
                source_stats[source]['errors'] += session_record.errors_count
        
        report = {
            "date": yesterday.strftime("%Y-%m-%d"),
            "total_sessions": len(sessions),
            "total_new_jobs": new_jobs,
            "source_breakdown": source_stats,
            "generated_at": datetime.utcnow().isoformat()
        }
        
        logger.info(f"Daily report generated: {report}")
        return report
        
    except Exception as e:
        logger.error(f"Error generating daily report: {e}")
        raise


@app.task(bind=True)
def test_scraper(self, source: str, search_term: str = "software engineer", location: str = "New York"):
    """Test a specific scraper with minimal data."""
    try:
        logger.info(f"Testing {source} scraper")
        
        scraper_map = {
            'indeed': IndeedScraper,
            'monster': MonsterScraper,
            'glassdoor': GlassdoorScraper,
            'linkedin': LinkedInScraper,
            'whatsjobs': WhatsJobsScraper,
            'dice': DiceScraper,
            'ziprecruiter': ZipRecruiterScraper
        }
        
        if source not in scraper_map:
            raise ValueError(f"Unknown source: {source}")
        
        scraper_class = scraper_map[source]
        scraper = scraper_class()
        
        self.update_state(state='PROGRESS', meta={'status': f'Testing {source} scraper'})
        
        # Test with minimal parameters
        stats = scraper.run_scraping([search_term], [location], max_pages=1)
        
        return {
            "status": "completed",
            "source": source,
            "test_params": {"search_term": search_term, "location": location},
            "stats": stats,
            "task_id": self.request.id
        }
        
    except Exception as e:
        logger.error(f"Error testing {source} scraper: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise
