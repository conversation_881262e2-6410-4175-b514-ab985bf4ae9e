#!/usr/bin/env python3
"""
Main script to run the job board scraper.
Can be used for testing individual scrapers or running full scraping jobs.
"""

import argparse
import logging
import sys
from typing import List

from config.settings import settings
from scrapers.indeed_scraper import IndeedScraper
from scrapers.monster_scraper import MonsterScraper
from scrapers.glassdoor_scraper import GlassdoorScraper
from database.database import create_tables

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(settings.log_file) if settings.log_file else logging.NullHandler()
    ]
)

logger = logging.getLogger(__name__)


def run_scraper(source: str, search_terms: List[str], locations: List[str], max_pages: int = 5):
    """Run a specific scraper."""
    logger.info(f"Starting {source} scraper")
    
    scraper_map = {
        'indeed': Indeed<PERSON><PERSON>raper,
        'monster': <PERSON>Scraper,
        'glassdoor': GlassdoorScraper
    }
    
    if source not in scraper_map:
        logger.error(f"Unknown scraper source: {source}")
        return False
    
    try:
        scraper_class = scraper_map[source]
        scraper = scraper_class()
        
        stats = scraper.run_scraping(search_terms, locations, max_pages)
        
        logger.info(f"Scraping completed successfully: {stats}")
        return True
        
    except Exception as e:
        logger.error(f"Error running {source} scraper: {e}")
        return False


def run_all_scrapers(search_terms: List[str], locations: List[str], max_pages: int = 5):
    """Run all enabled scrapers."""
    logger.info("Starting all scrapers")
    
    results = {}
    
    if settings.indeed_enabled:
        logger.info("Running Indeed scraper")
        results['indeed'] = run_scraper('indeed', search_terms, locations, max_pages)
    
    if settings.monster_enabled:
        logger.info("Running Monster scraper")
        results['monster'] = run_scraper('monster', search_terms, locations, max_pages)
    
    if settings.glassdoor_enabled:
        logger.info("Running Glassdoor scraper")
        results['glassdoor'] = run_scraper('glassdoor', search_terms, locations, max_pages)
    
    # Summary
    successful = sum(1 for success in results.values() if success)
    total = len(results)
    
    logger.info(f"Scraping summary: {successful}/{total} scrapers completed successfully")
    
    for source, success in results.items():
        status = "✓" if success else "✗"
        logger.info(f"  {status} {source}")
    
    return results


def test_scraper(source: str):
    """Test a scraper with minimal parameters."""
    logger.info(f"Testing {source} scraper")
    
    test_search_terms = ["software engineer"]
    test_locations = ["New York"]
    test_max_pages = 1
    
    return run_scraper(source, test_search_terms, test_locations, test_max_pages)


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Job Board Scraper")
    
    parser.add_argument(
        '--source',
        choices=['indeed', 'monster', 'glassdoor', 'all'],
        default='all',
        help='Scraper source to run'
    )
    
    parser.add_argument(
        '--search-terms',
        nargs='+',
        default=['software engineer', 'data scientist'],
        help='Job search terms'
    )
    
    parser.add_argument(
        '--locations',
        nargs='+',
        default=['New York', 'San Francisco', 'Remote'],
        help='Locations to search'
    )
    
    parser.add_argument(
        '--max-pages',
        type=int,
        default=5,
        help='Maximum pages to scrape per source'
    )
    
    parser.add_argument(
        '--test',
        action='store_true',
        help='Run in test mode with minimal parameters'
    )
    
    parser.add_argument(
        '--init-db',
        action='store_true',
        help='Initialize database tables'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Set verbose logging if requested
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Initialize database if requested
    if args.init_db:
        logger.info("Initializing database tables")
        try:
            create_tables()
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Error creating database tables: {e}")
            return 1
    
    # Run scrapers
    try:
        if args.test:
            if args.source == 'all':
                logger.info("Testing all scrapers")
                success = True
                for source in ['indeed', 'monster', 'glassdoor']:
                    if not test_scraper(source):
                        success = False
                return 0 if success else 1
            else:
                return 0 if test_scraper(args.source) else 1
        else:
            if args.source == 'all':
                results = run_all_scrapers(args.search_terms, args.locations, args.max_pages)
                return 0 if all(results.values()) else 1
            else:
                return 0 if run_scraper(args.source, args.search_terms, args.locations, args.max_pages) else 1
                
    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
