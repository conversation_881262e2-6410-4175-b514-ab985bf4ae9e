import time
import random
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from urllib.parse import urlencode, urljoin
import logging
import re

from bs4 import BeautifulSoup
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from scrapers.base_scraper import BaseScraper

logger = logging.getLogger(__name__)


class IndeedScraper(BaseScraper):
    """Scraper for Indeed job postings."""
    
    def __init__(self):
        super().__init__("indeed")
        self.base_url = "https://www.indeed.com"
        self.search_url = "https://www.indeed.com/jobs"
    
    def scrape_jobs(self, search_terms: List[str], locations: List[str], max_pages: int = 10) -> int:
        """Scrape jobs from Indeed."""
        total_jobs = 0
        
        for search_term in search_terms:
            for location in locations:
                logger.info(f"Scraping Indeed for '{search_term}' in '{location}'")
                jobs_found = self._scrape_search_results(search_term, location, max_pages)
                total_jobs += jobs_found
                
                # Add delay between different search combinations
                time.sleep(random.uniform(5, 10))
        
        return total_jobs
    
    def _scrape_search_results(self, search_term: str, location: str, max_pages: int) -> int:
        """Scrape search results for a specific term and location."""
        jobs_found = 0
        
        for page in range(max_pages):
            try:
                # Build search URL
                params = {
                    'q': search_term,
                    'l': location,
                    'start': page * 10,  # Indeed shows 10 jobs per page
                    'sort': 'date',  # Sort by date to get newest first
                }
                
                search_url = f"{self.search_url}?{urlencode(params)}"
                logger.debug(f"Scraping page {page + 1}: {search_url}")
                
                # Make request
                response = self._make_request(search_url)
                if not response:
                    logger.warning(f"Failed to fetch page {page + 1}")
                    continue
                
                soup = BeautifulSoup(response.text, 'html.parser')
                self.stats['pages_scraped'] += 1
                
                # Find job listings
                job_cards = soup.find_all('div', {'class': re.compile(r'job_seen_beacon|jobsearch-SerpJobCard')})
                
                if not job_cards:
                    logger.warning(f"No job cards found on page {page + 1}")
                    break
                
                # Parse each job listing
                for job_card in job_cards:
                    job_data = self.parse_job_listing(job_card)
                    if job_data:
                        if self._save_job(job_data):
                            jobs_found += 1
                
                # Check if there are more pages
                next_page = soup.find('a', {'aria-label': 'Next Page'})
                if not next_page:
                    logger.info(f"No more pages available after page {page + 1}")
                    break
                
                # Add delay between pages
                time.sleep(random.uniform(2, 5))
                
            except Exception as e:
                logger.error(f"Error scraping page {page + 1}: {e}")
                self.stats['errors'] += 1
                continue
        
        return jobs_found
    
    def parse_job_listing(self, job_element) -> Optional[Dict[str, Any]]:
        """Parse a single Indeed job listing."""
        try:
            job_data = {}
            
            # Title and URL
            title_element = job_element.find('h2', {'class': re.compile(r'jobTitle')})
            if not title_element:
                return None
            
            title_link = title_element.find('a')
            if title_link:
                job_data['title'] = title_link.get_text(strip=True)
                job_data['source_url'] = urljoin(self.base_url, title_link.get('href', ''))
            else:
                # Sometimes title is in span without link
                job_data['title'] = title_element.get_text(strip=True)
                job_data['source_url'] = self.base_url  # Fallback
            
            # Company
            company_element = job_element.find('span', {'class': re.compile(r'companyName')})
            if company_element:
                company_link = company_element.find('a')
                if company_link:
                    job_data['company'] = company_link.get_text(strip=True)
                else:
                    job_data['company'] = company_element.get_text(strip=True)
            
            # Location
            location_element = job_element.find('div', {'class': re.compile(r'companyLocation')})
            if location_element:
                job_data['location'] = location_element.get_text(strip=True)
            
            # Salary
            salary_element = job_element.find('span', {'class': re.compile(r'salary-snippet')})
            if salary_element:
                salary_text = salary_element.get_text(strip=True)
                salary_info = self._parse_salary(salary_text)
                job_data.update(salary_info)
            
            # Job snippet/description
            snippet_element = job_element.find('div', {'class': re.compile(r'job-snippet')})
            if snippet_element:
                job_data['description'] = snippet_element.get_text(strip=True)
            
            # Posted date
            date_element = job_element.find('span', {'class': re.compile(r'date')})
            if date_element:
                posted_date = self._parse_posted_date(date_element.get_text(strip=True))
                if posted_date:
                    job_data['posted_date'] = posted_date
            
            # Job type (if available)
            job_type_element = job_element.find('span', {'class': re.compile(r'jobType')})
            if job_type_element:
                job_data['job_type'] = job_type_element.get_text(strip=True)
            
            # Check for remote work
            remote_indicators = ['remote', 'work from home', 'telecommute']
            job_text = f"{job_data.get('title', '')} {job_data.get('description', '')}".lower()
            job_data['is_remote'] = any(indicator in job_text for indicator in remote_indicators)
            
            # Set default values
            job_data.setdefault('job_type', 'full-time')
            job_data.setdefault('is_remote', False)
            job_data.setdefault('salary_currency', 'USD')
            
            return job_data
            
        except Exception as e:
            logger.error(f"Error parsing job listing: {e}")
            return None
    
    def _parse_salary(self, salary_text: str) -> Dict[str, Any]:
        """Parse salary information from text."""
        salary_info = {}
        
        try:
            # Remove currency symbols and normalize
            clean_text = re.sub(r'[,$]', '', salary_text.lower())
            
            # Look for salary ranges
            range_match = re.search(r'(\d+(?:\.\d+)?)\s*(?:k|000)?\s*[-–]\s*(\d+(?:\.\d+)?)\s*(?:k|000)?', clean_text)
            if range_match:
                min_sal, max_sal = range_match.groups()
                
                # Convert k notation
                if 'k' in clean_text or '000' in salary_text:
                    salary_info['salary_min'] = float(min_sal) * 1000
                    salary_info['salary_max'] = float(max_sal) * 1000
                else:
                    salary_info['salary_min'] = float(min_sal)
                    salary_info['salary_max'] = float(max_sal)
            else:
                # Look for single salary value
                single_match = re.search(r'(\d+(?:\.\d+)?)\s*(?:k|000)?', clean_text)
                if single_match:
                    salary = float(single_match.group(1))
                    if 'k' in clean_text or '000' in salary_text:
                        salary *= 1000
                    salary_info['salary_min'] = salary
                    salary_info['salary_max'] = salary
            
            # Determine salary period
            if 'hour' in clean_text:
                salary_info['salary_period'] = 'hourly'
            elif 'month' in clean_text:
                salary_info['salary_period'] = 'monthly'
            else:
                salary_info['salary_period'] = 'yearly'
            
            salary_info['salary_currency'] = 'USD'
            
        except Exception as e:
            logger.debug(f"Error parsing salary '{salary_text}': {e}")
        
        return salary_info
    
    def _parse_posted_date(self, date_text: str) -> Optional[datetime]:
        """Parse posted date from text like 'Posted 2 days ago'."""
        try:
            date_text = date_text.lower().strip()
            now = datetime.utcnow()
            
            if 'today' in date_text or 'just posted' in date_text:
                return now
            elif 'yesterday' in date_text:
                return now - timedelta(days=1)
            else:
                # Look for "X days ago" or "X hours ago"
                days_match = re.search(r'(\d+)\s*days?\s*ago', date_text)
                if days_match:
                    days = int(days_match.group(1))
                    return now - timedelta(days=days)
                
                hours_match = re.search(r'(\d+)\s*hours?\s*ago', date_text)
                if hours_match:
                    hours = int(hours_match.group(1))
                    return now - timedelta(hours=hours)
            
            return None
            
        except Exception as e:
            logger.debug(f"Error parsing date '{date_text}': {e}")
            return None
    
    def get_job_details(self, job_url: str) -> Optional[Dict[str, Any]]:
        """Get detailed job information from job detail page."""
        try:
            response = self._make_request(job_url)
            if not response:
                return None
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            details = {}
            
            # Full job description
            description_element = soup.find('div', {'class': re.compile(r'jobsearch-jobDescriptionText')})
            if description_element:
                details['description'] = description_element.get_text(strip=True)
            
            # Benefits
            benefits_element = soup.find('div', {'class': re.compile(r'benefits')})
            if benefits_element:
                details['benefits'] = benefits_element.get_text(strip=True)
            
            # Skills/qualifications
            qualifications_element = soup.find('div', {'class': re.compile(r'qualifications')})
            if qualifications_element:
                details['skills_required'] = qualifications_element.get_text(strip=True)
            
            return details
            
        except Exception as e:
            logger.error(f"Error getting job details from {job_url}: {e}")
            return None
