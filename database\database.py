from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from contextlib import contextmanager
from typing import Generator
import logging

from config.settings import settings
from database.models import Base

logger = logging.getLogger(__name__)

# Create database engine
engine = create_engine(
    settings.database_url,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=False,  # Set to True for SQL debugging
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def create_tables():
    """Create all database tables."""
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error creating database tables: {e}")
        raise


def drop_tables():
    """Drop all database tables (use with caution!)."""
    try:
        Base.metadata.drop_all(bind=engine)
        logger.info("Database tables dropped successfully")
    except Exception as e:
        logger.error(f"Error dropping database tables: {e}")
        raise


@contextmanager
def get_db_session() -> Generator[Session, None, None]:
    """
    Context manager for database sessions.
    Ensures proper cleanup and error handling.
    """
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error(f"Database session error: {e}")
        raise
    finally:
        session.close()


def get_db() -> Generator[Session, None, None]:
    """
    Dependency for FastAPI to get database session.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


class DatabaseManager:
    """Database manager for common operations."""
    
    @staticmethod
    def health_check() -> bool:
        """Check if database is accessible."""
        try:
            with get_db_session() as session:
                session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    @staticmethod
    def get_table_counts() -> dict:
        """Get row counts for all tables."""
        try:
            with get_db_session() as session:
                from database.models import JobPosting, ScrapingSession, ProxyStatus
                
                counts = {
                    'job_postings': session.query(JobPosting).count(),
                    'scraping_sessions': session.query(ScrapingSession).count(),
                    'proxy_status': session.query(ProxyStatus).count(),
                }
                return counts
        except Exception as e:
            logger.error(f"Error getting table counts: {e}")
            return {}
    
    @staticmethod
    def cleanup_old_data(days: int = 30):
        """Clean up old data to maintain database performance."""
        try:
            from datetime import datetime, timedelta
            from database.models import JobPosting, ScrapingSession
            
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            with get_db_session() as session:
                # Remove old inactive job postings
                old_jobs = session.query(JobPosting).filter(
                    JobPosting.is_active == False,
                    JobPosting.updated_at < cutoff_date
                ).delete()
                
                # Remove old scraping sessions
                old_sessions = session.query(ScrapingSession).filter(
                    ScrapingSession.started_at < cutoff_date
                ).delete()
                
                logger.info(f"Cleaned up {old_jobs} old job postings and {old_sessions} old scraping sessions")
                
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            raise


# Initialize database on import
if __name__ == "__main__":
    create_tables()
