import time
import random
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from urllib.parse import urlencode, urljoin
import logging
import re

from bs4 import BeautifulSoup
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from scrapers.base_scraper import BaseScraper

logger = logging.getLogger(__name__)


class DiceScraper(BaseScraper):
    """Scraper for Dice job postings (tech-focused job board)."""
    
    def __init__(self):
        super().__init__("dice")
        self.base_url = "https://www.dice.com"
        self.search_url = "https://www.dice.com/jobs"
    
    def scrape_jobs(self, search_terms: List[str], locations: List[str], max_pages: int = 10) -> int:
        """Scrape jobs from Dice."""
        total_jobs = 0
        
        for search_term in search_terms:
            for location in locations:
                logger.info(f"Scraping <PERSON><PERSON> for '{search_term}' in '{location}'")
                jobs_found = self._scrape_search_results(search_term, location, max_pages)
                total_jobs += jobs_found
                
                # Add delay between different search combinations
                time.sleep(random.uniform(5, 10))
        
        return total_jobs
    
    def _scrape_search_results(self, search_term: str, location: str, max_pages: int) -> int:
        """Scrape search results for a specific term and location."""
        jobs_found = 0
        
        for page in range(max_pages):
            try:
                # Build search URL
                params = {
                    'q': search_term,
                    'location': location,
                    'page': page + 1,
                    'filters.postedDate': 'ONE',  # Posted in last day
                    'filters.employmentType': 'CONTRACTS|FULLTIME',
                    'filters.workFromHomeAvailability': 'true',
                    'sort': 'date'
                }
                
                search_url = f"{self.search_url}?{urlencode(params)}"
                logger.debug(f"Scraping page {page + 1}: {search_url}")
                
                # Make request
                response = self._make_request(search_url)
                if not response:
                    logger.warning(f"Failed to fetch page {page + 1}")
                    continue
                
                soup = BeautifulSoup(response.text, 'html.parser')
                self.stats['pages_scraped'] += 1
                
                # Find job listings
                job_cards = soup.find_all('div', {'class': re.compile(r'card|search-result-job')})
                
                if not job_cards:
                    # Try alternative selectors for Dice
                    job_cards = soup.find_all('div', {'data-cy': 'search-result-job'})
                
                if not job_cards:
                    logger.warning(f"No job cards found on page {page + 1}")
                    break
                
                # Parse each job listing
                for job_card in job_cards:
                    job_data = self.parse_job_listing(job_card)
                    if job_data:
                        if self._save_job(job_data):
                            jobs_found += 1
                
                # Check if there are more pages
                next_page = soup.find('a', {'data-cy': 'pagination-next'})
                if not next_page or 'disabled' in next_page.get('class', []):
                    logger.info(f"No more pages available after page {page + 1}")
                    break
                
                # Add delay between pages
                time.sleep(random.uniform(3, 6))
                
            except Exception as e:
                logger.error(f"Error scraping page {page + 1}: {e}")
                self.stats['errors'] += 1
                continue
        
        return jobs_found
    
    def parse_job_listing(self, job_element) -> Optional[Dict[str, Any]]:
        """Parse a single Dice job listing."""
        try:
            job_data = {}
            
            # Title and URL
            title_element = job_element.find('a', {'data-cy': 'job-title'})
            if not title_element:
                title_element = job_element.find('h5') or job_element.find('h4')
                if title_element:
                    title_link = title_element.find('a')
                    if title_link:
                        title_element = title_link
            
            if not title_element:
                return None
            
            job_data['title'] = title_element.get_text(strip=True)
            href = title_element.get('href', '')
            if href.startswith('/'):
                job_data['source_url'] = urljoin(self.base_url, href)
            else:
                job_data['source_url'] = href
            
            # Company
            company_element = job_element.find('a', {'data-cy': 'search-result-company-name'})
            if not company_element:
                company_element = job_element.find('span', {'class': re.compile(r'company')})
            
            if company_element:
                job_data['company'] = company_element.get_text(strip=True)
            
            # Location
            location_element = job_element.find('li', {'data-cy': 'job-location'})
            if not location_element:
                location_element = job_element.find('span', {'class': re.compile(r'location')})
            
            if location_element:
                job_data['location'] = location_element.get_text(strip=True)
            
            # Salary
            salary_element = job_element.find('span', {'data-cy': 'search-result-salary'})
            if not salary_element:
                salary_element = job_element.find('div', {'class': re.compile(r'salary|pay')})
            
            if salary_element:
                salary_text = salary_element.get_text(strip=True)
                salary_info = self._parse_salary(salary_text)
                job_data.update(salary_info)
            
            # Job snippet/description
            snippet_element = job_element.find('div', {'data-cy': 'search-result-summary'})
            if not snippet_element:
                snippet_element = job_element.find('div', {'class': re.compile(r'summary|snippet|description')})
            
            if snippet_element:
                job_data['description'] = snippet_element.get_text(strip=True)
            
            # Posted date
            date_element = job_element.find('span', {'data-cy': 'search-result-posted-date'})
            if not date_element:
                date_element = job_element.find('time')
            
            if date_element:
                date_text = date_element.get('datetime') or date_element.get_text(strip=True)
                posted_date = self._parse_posted_date(date_text)
                if posted_date:
                    job_data['posted_date'] = posted_date
            
            # Job type (Dice often shows contract vs full-time)
            job_type_element = job_element.find('span', {'data-cy': 'search-result-employment-type'})
            if job_type_element:
                job_type_text = job_type_element.get_text(strip=True).lower()
                if 'contract' in job_type_text:
                    job_data['job_type'] = 'contract'
                elif 'full' in job_type_text:
                    job_data['job_type'] = 'full-time'
                elif 'part' in job_type_text:
                    job_data['job_type'] = 'part-time'
            
            # Skills (Dice often shows required skills)
            skills_elements = job_element.find_all('span', {'class': re.compile(r'skill|tag')})
            if skills_elements:
                skills = [skill.get_text(strip=True) for skill in skills_elements]
                job_data['skills_required'] = ', '.join(skills)
            
            # Check for remote work
            remote_indicators = ['remote', 'work from home', 'telecommute', 'virtual', 'anywhere']
            job_text = f"{job_data.get('title', '')} {job_data.get('description', '')} {job_data.get('location', '')}".lower()
            job_data['is_remote'] = any(indicator in job_text for indicator in remote_indicators)
            
            # Set default values
            job_data.setdefault('job_type', 'full-time')
            job_data.setdefault('is_remote', False)
            job_data.setdefault('salary_currency', 'USD')
            
            return job_data
            
        except Exception as e:
            logger.error(f"Error parsing job listing: {e}")
            return None
    
    def _parse_salary(self, salary_text: str) -> Dict[str, Any]:
        """Parse salary information from Dice format."""
        salary_info = {}
        
        try:
            # Remove currency symbols and normalize
            clean_text = re.sub(r'[,$]', '', salary_text.lower())
            
            # Dice often shows hourly rates for contracts
            if '/hr' in clean_text or 'hour' in clean_text:
                # Look for hourly ranges
                range_match = re.search(r'(\d+(?:\.\d+)?)\s*[-–]\s*(\d+(?:\.\d+)?)', clean_text)
                if range_match:
                    min_sal, max_sal = range_match.groups()
                    salary_info['salary_min'] = float(min_sal)
                    salary_info['salary_max'] = float(max_sal)
                else:
                    # Single hourly rate
                    single_match = re.search(r'(\d+(?:\.\d+)?)', clean_text)
                    if single_match:
                        salary = float(single_match.group(1))
                        salary_info['salary_min'] = salary
                        salary_info['salary_max'] = salary
                
                salary_info['salary_period'] = 'hourly'
            
            else:
                # Annual salary
                range_match = re.search(r'(\d+(?:\.\d+)?)\s*(?:k|000)?\s*[-–]\s*(\d+(?:\.\d+)?)\s*(?:k|000)?', clean_text)
                if range_match:
                    min_sal, max_sal = range_match.groups()
                    
                    # Convert k notation
                    if 'k' in clean_text:
                        salary_info['salary_min'] = float(min_sal) * 1000
                        salary_info['salary_max'] = float(max_sal) * 1000
                    else:
                        salary_info['salary_min'] = float(min_sal)
                        salary_info['salary_max'] = float(max_sal)
                else:
                    # Single salary value
                    single_match = re.search(r'(\d+(?:\.\d+)?)\s*(?:k|000)?', clean_text)
                    if single_match:
                        salary = float(single_match.group(1))
                        if 'k' in clean_text:
                            salary *= 1000
                        salary_info['salary_min'] = salary
                        salary_info['salary_max'] = salary
                
                salary_info['salary_period'] = 'yearly'
            
            salary_info['salary_currency'] = 'USD'
            
        except Exception as e:
            logger.debug(f"Error parsing salary '{salary_text}': {e}")
        
        return salary_info
    
    def _parse_posted_date(self, date_text: str) -> Optional[datetime]:
        """Parse posted date from Dice format."""
        try:
            date_text = date_text.lower().strip()
            now = datetime.utcnow()
            
            # Handle ISO format first
            if 'T' in date_text and ('Z' in date_text or '+' in date_text):
                return datetime.fromisoformat(date_text.replace('Z', '+00:00'))
            
            if 'today' in date_text or 'just posted' in date_text:
                return now
            elif 'yesterday' in date_text:
                return now - timedelta(days=1)
            else:
                # Look for "X days ago" or "X hours ago"
                days_match = re.search(r'(\d+)\s*days?\s*ago', date_text)
                if days_match:
                    days = int(days_match.group(1))
                    return now - timedelta(days=days)
                
                hours_match = re.search(r'(\d+)\s*hours?\s*ago', date_text)
                if hours_match:
                    hours = int(hours_match.group(1))
                    return now - timedelta(hours=hours)
                
                weeks_match = re.search(r'(\d+)\s*weeks?\s*ago', date_text)
                if weeks_match:
                    weeks = int(weeks_match.group(1))
                    return now - timedelta(weeks=weeks)
            
            return None
            
        except Exception as e:
            logger.debug(f"Error parsing date '{date_text}': {e}")
            return None
    
    def get_job_details(self, job_url: str) -> Optional[Dict[str, Any]]:
        """Get detailed job information from Dice job detail page."""
        try:
            response = self._make_request(job_url)
            if not response:
                return None
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            details = {}
            
            # Full job description
            description_element = soup.find('div', {'data-cy': 'jobDescription'})
            if not description_element:
                description_element = soup.find('div', {'class': re.compile(r'job-description|description')})
            
            if description_element:
                details['description'] = description_element.get_text(strip=True)
            
            # Skills
            skills_section = soup.find('div', {'data-cy': 'skillsList'})
            if skills_section:
                skills = [skill.get_text(strip=True) for skill in skills_section.find_all('span')]
                details['skills_required'] = ', '.join(skills)
            
            # Benefits
            benefits_element = soup.find('div', {'class': re.compile(r'benefits|perks')})
            if benefits_element:
                details['benefits'] = benefits_element.get_text(strip=True)
            
            # Experience level
            experience_element = soup.find('span', {'class': re.compile(r'experience|level')})
            if experience_element:
                details['experience_level'] = experience_element.get_text(strip=True)
            
            return details
            
        except Exception as e:
            logger.error(f"Error getting job details from {job_url}: {e}")
            return None
